# 千帆封面图生成工具 - 使用指南

## 🚀 快速开始

### 第一步：安装
1. 双击 `install.bat` 自动安装所有依赖
2. 等待安装完成（可能需要几分钟）

### 第二步：启动
1. 双击 `run.bat` 启动程序
2. 程序会打开图形界面

### 第三步：使用
选择"单个处理"或"批量处理"开始生成封面

## 📋 详细使用说明

### 单个处理模式

1. **输入学院名称**
   - 在文本框中输入完整的学院名称
   - 例如：`电子科技大学经济与管理学院`

2. **选择主题色**（可选）
   - 默认选择"自动选择"，AI会智能推荐
   - 也可以手动选择特定主题色

3. **预览效果**
   - 点击"预览效果"查看生成效果
   - 确认无误后点击"确认生成"

4. **生成封面**
   - 点击"生成封面"开始处理
   - 等待进度条完成
   - 程序会询问是否打开输出文件夹

### 批量处理模式

1. **准备Excel文件**
   ```
   学院名称
   西南财经大学管理科学与工程学院
   中南财经政法大学经济学院
   北京外国语大学国际商学院
   ...
   ```

2. **选择文件**
   - 点击"浏览"选择Excel文件
   - 程序会显示文件信息和示例数据

3. **配置选项**
   - ✅ 自动选择主题色：让AI为每个学院选择合适的主题
   - ✅ 创建批量处理文件夹：将所有结果放在一个文件夹中

4. **开始处理**
   - 点击"开始批量处理"
   - 可以随时点击"停止处理"中断
   - 处理完成后会显示成功统计

## 🎨 主题色说明

| 主题 | 适用学院类型 | 视觉效果 |
|------|-------------|----------|
| 科技蓝 | 理工科、计算机、电子信息 | 深蓝渐变，现代科技感 |
| 商务金 | 商学院、经济管理 | 金色渐变，商务专业感 |
| 学术绿 | 文学院、教育、人文社科 | 绿色渐变，学术氛围 |
| 医学红 | 医学院、生命科学 | 红色渐变，医学专业感 |
| 法政紫 | 法学院、政治、公共管理 | 紫色渐变，庄重权威感 |
| 艺术橙 | 艺术学院、设计、传媒 | 橙色渐变，创意活力感 |
| 工程灰 | 工程学院、建筑、土木 | 灰色渐变，工程稳重感 |

## 📁 输出文件说明

### 文件结构
```
output/
├── 电子科技大学_经济与管理学院/
│   ├── 电子科技大学_经济与管理学院_封面设计.html
│   └── 电子科技大学_经济与管理学院_封面图.png
└── 批量生成_20250112_143022/
    ├── 西南财经大学_管理科学与工程学院/
    └── 中南财经政法大学_经济学院/
```

### 文件说明
- **HTML文件**：可以在浏览器中打开查看效果，也可以进一步编辑
- **PNG文件**：高清图片文件，可直接使用或打印

## ⚙️ 设置说明

### API设置
- **API Key**：DeepSeek API密钥（已预设，一般无需修改）
- **测试连接**：验证API是否可用

### 输出设置
- **输出目录**：生成文件的保存位置
- **缩放比例**：图片清晰度（2.0 = 高清，4.0 = 超高清）
- **自动裁剪白边**：自动去除图片周围的空白区域

## 🔧 故障排除

### 程序无法启动
- 确保Python版本3.8+
- 重新运行 `install.bat`
- 检查是否有杀毒软件阻止

### API连接失败
- 检查网络连接
- 在设置中点击"测试连接"
- 如果持续失败，程序会使用备用分析方案

### 图片生成失败
- 检查输出目录是否有写入权限
- 尝试降低缩放比例到1.0
- 确保playwright浏览器已正确安装

### Excel读取失败
- 确保文件格式为.xlsx或.xls
- 检查第一列是否包含学院名称
- 关闭Excel程序后重试

## 💡 使用技巧

1. **批量处理建议**
   - 一次处理不超过50个学院（避免超时）
   - 大批量可以分多次处理

2. **主题选择建议**
   - 让AI自动选择通常效果最好
   - 特殊需求可以手动指定主题

3. **文件管理建议**
   - 定期清理output文件夹
   - 重要文件及时备份

4. **性能优化建议**
   - 关闭不必要的程序释放内存
   - 网络不稳定时可以多次重试

## 📞 技术支持

如果遇到问题：
1. 查看程序界面中的日志信息
2. 参考本使用指南的故障排除部分
3. 检查README.md中的详细说明

## 🎯 最佳实践

1. **学院名称输入**
   - 使用完整的官方名称
   - 包含大学名称和学院名称
   - 避免使用简称或缩写

2. **批量处理**
   - Excel第一列专门放学院名称
   - 避免空行和无效数据
   - 建议先用小批量测试

3. **文件输出**
   - 定期整理输出文件夹
   - 重要文件及时备份
   - 可以自定义输出目录

---

**祝您使用愉快！** 🎉
