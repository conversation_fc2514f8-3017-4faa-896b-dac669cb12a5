@echo off
cd /d "%~dp0"

echo ========================================
echo Cover Generator Tool v1.0.0
echo ========================================
echo.

python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python not found
    echo Please install Python and add to PATH
    echo Download: https://www.python.org/
    echo.
    pause
    exit /b 1
)

if not exist "main.py" (
    echo Error: main.py not found
    echo Current directory: %CD%
    echo Please run this script in the correct directory
    echo.
    pause
    exit /b 1
)

echo Starting Cover Generator Tool...
echo.

python main.py

echo.
echo Program exited
pause
