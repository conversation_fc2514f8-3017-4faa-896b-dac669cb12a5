@echo off
chcp 65001 >nul
echo ========================================
echo 千帆封面图生成工具 v1.0.0
echo ========================================
echo.

REM 获取脚本所在目录
set SCRIPT_DIR=%~dp0

REM 切换到脚本目录
cd /d "%SCRIPT_DIR%"

REM 检查Python是否可用
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误：未找到Python，请确保Python已正确安装并添加到PATH环境变量
    echo.
    echo 请访问 https://www.python.org/ 下载并安装Python
    pause
    exit /b 1
)

REM 检查main.py是否存在
if not exist "main.py" (
    echo 错误：未找到main.py文件
    echo 当前目录：%CD%
    echo 请确保在正确的目录中运行此脚本
    pause
    exit /b 1
)

echo 正在启动千帆封面图生成工具...
echo.

REM 启动程序
python main.py

echo.
echo 程序已退出
pause
