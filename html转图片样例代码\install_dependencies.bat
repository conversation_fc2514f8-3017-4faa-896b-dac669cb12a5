@echo off
chcp 65001 > nul
title 安装HTML转图片工具依赖

echo ========================================
echo   HTML转图片工具 - 依赖安装程序 v2.1
echo ========================================
echo.

cd /d "%~dp0"

:: 检查Python是否安装
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 未找到Python，请先安装Python 3.8+
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo [信息] 开始安装依赖包...
echo.

:: 升级pip
echo [1/8] 升级pip...
python -m pip install --upgrade pip

:: 安装PyQt6（优先）
echo [2/8] 安装PyQt6界面框架...
python -m pip install PyQt6>=6.5.0

:: 安装playwright
echo [3/8] 安装playwright...
python -m pip install playwright>=1.40.0

:: 安装图像处理库
echo [4/8] 安装图像处理库...
python -m pip install Pillow>=10.0.0 opencv-python>=4.8.0 numpy>=1.24.0

:: 安装PDF处理库
echo [5/8] 安装PDF处理库...
python -m pip install PyPDF2>=3.0.0 pymupdf>=1.23.0

:: 安装playwright浏览器
echo [6/8] 安装playwright浏览器...
python -m playwright install chromium

echo [7/8] 验证安装...
python -c "import PyQt6; print('✓ PyQt6安装成功')" 2>nul || echo "⚠ PyQt6安装失败，将使用tkinter界面"
python -c "import playwright; print('✓ playwright安装成功')" 2>nul || echo "✗ playwright安装失败"
python -c "import PIL; print('✓ Pillow安装成功')" 2>nul || echo "✗ Pillow安装失败"

echo [8/8] 安装完成！
echo.
echo ========================================
echo   所有依赖安装完成！
echo   现在可以运行 run.bat 启动程序
echo ========================================
pause

echo [信息] 正在安装Python依赖包...
echo.

:: 升级pip
echo 升级pip...
python -m pip install --upgrade pip

:: 安装依赖包
echo.
echo 安装项目依赖...
python -m pip install -r requirements.txt

:: 安装playwright浏览器
echo.
echo 安装Playwright浏览器...
python -m playwright install chromium

echo.
echo ========================================
echo   依赖安装完成！
echo ========================================
echo.
echo 现在可以运行 run.bat 启动程序了
echo.

pause
