# 千帆封面生成器

一个智能的学院封面生成工具，基于DeepSeek AI技术，自动识别学校学院信息并生成精美的保研封面。

## 功能特点

- 🤖 **智能识别**: 自动解析学校学院名称，智能匹配主题色
- 🎨 **精美设计**: 基于工商经管专业设计，多种主题色可选
- 📱 **简洁界面**: 直观的GUI界面，操作简单易用
- 🚀 **批量处理**: 支持Excel批量导入，一键生成多个封面
- 🖼️ **双格式输出**: 同时生成HTML和高清PNG格式
- 📁 **自动整理**: 智能文件夹管理，输出文件自动分类

## 快速开始

### 1. 安装依赖

```bash
# 安装Python依赖包
pip install openai pandas playwright openpyxl

# 安装Playwright浏览器
playwright install chromium
```

### 2. 运行程序

```bash
# 直接运行主程序
python main.py
```

### 3. 使用说明

1. **API测试**: 首次运行请先测试DeepSeek API连接
2. **单个生成**: 在输入框中输入学校学院名称，如"电子科技大学经济与管理学院"
3. **批量生成**: 准备Excel文件（第一列为学校学院名称），选择文件后点击批量生成
4. **查看结果**: 生成完成后可直接打开输出文件夹查看结果

## 文件结构

```
cover_generator/
├── main.py                 # 主程序入口
├── src/                    # 源代码目录
│   ├── __init__.py        # 包初始化
│   ├── config.py          # 配置文件
│   ├── deepseek_client.py # AI客户端
│   ├── html_generator.py  # HTML生成器
│   ├── image_converter.py # 图片转换器
│   ├── excel_processor.py # Excel处理器
│   ├── file_manager.py    # 文件管理器
│   ├── controller.py      # 核心控制器
│   └── gui.py            # 图形界面
├── templates/             # 模板文件
├── output/               # 输出目录
└── README.md            # 说明文档
```

## 支持的学院类型

- 💼 商学院、工商管理学院
- 💰 经济学院、金融学院
- 📊 管理科学与工程学院
- 🌐 国际商学院、国际贸易
- ⚡ 电子信息、计算机学院
- 📚 其他各类学院

## 主题色方案

- **科技蓝**: 适合电子信息、计算机等科技类学院
- **商务绿**: 适合商学院、商务管理类
- **财经金**: 适合经济、金融、会计类学院
- **管理紫**: 适合管理学院、MBA/MPA等
- **经济红**: 适合经济学院、政法类
- **学院蓝**: 通用学院配色

## 输出示例

每个学校会生成独立的文件夹，包含：
- `学校名_学院名_封面设计.html` - 可编辑的HTML源文件
- `学校名_学院名_封面图.png` - 高清PNG图片文件

## 技术栈

- **后端**: Python 3.7+
- **AI**: DeepSeek API
- **界面**: Tkinter
- **HTML转图**: Playwright
- **数据处理**: Pandas
- **图像处理**: PIL/Pillow

## 注意事项

1. 确保网络连接正常，需要访问DeepSeek API
2. 首次使用Playwright需要下载浏览器组件
3. Excel文件第一列应包含完整的学校学院名称
4. 生成的图片为高清PNG格式，文件较大

## 更新日志

### v1.0.0 (2025-01-XX)
- 初始版本发布
- 支持单个和批量生成
- 集成DeepSeek AI智能识别
- 完整的GUI界面
- 高质量HTML转PNG功能

## 联系方式

如有问题或建议，请联系开发团队。

---

**千帆封面生成器** - 让保研资料制作更简单 ✨
