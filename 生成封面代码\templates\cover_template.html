<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产品封面生成器 - {{UNIVERSITY}} {{COLLEGE}}</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700;900&display=swap');
        
        /* --- 全局样式和主题配色 --- */
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f8fafc;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            padding: 20px;
            margin: 0;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        #cover-container {
            width: 750px;
            height: 1000px;
            /* 背景色：主题渐变 */
            background: {{PRIMARY_GRADIENT}};
            color: {{TEXT_PRIMARY}};
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            padding: 50px;
            box-sizing: border-box;
            border-radius: 8px;
            overflow: hidden;
            position: relative;
            text-align: center;
            box-shadow: 0 15px 30px rgba(0,0,0,0.15);
        }

        /* --- 装饰性元素 --- */
        #cover-container::before {
            content: '';
            position: absolute;
            top: -150px;
            left: -200px;
            width: 600px;
            height: 600px;
            background: radial-gradient(circle, {{DECORATION_COLOR}} 0%, rgba(251, 191, 36, 0) 70%);
            transform: rotate(45deg);
        }
        #cover-container::after {
            content: '';
            position: absolute;
            bottom: -200px;
            right: -200px;
            width: 550px;
            height: 550px;
            background: radial-gradient(circle, {{DECORATION_COLOR}} 0%, rgba(251, 191, 36, 0) 70%);
            border-radius: 50%;
        }

        .main-focus {
            z-index: 1;
            margin-top: 40px;
        }
        
        /* --- 学院名称样式 --- */
        .main-focus .school-name {
            font-size: 28px;
            font-weight: 500;
            background-color: rgba(0, 0, 0, 0.2);
            color: {{TEXT_ACCENT}};
            padding: 12px 35px;
            border-radius: 50px;
            display: inline-block;
            margin-bottom: 30px;
            border: 1px solid {{BORDER_COLOR}};
        }

        .main-focus .main-title {
            font-weight: 900;
            text-shadow: none;
            color: {{TEXT_PRIMARY}};
            margin: 0;
            line-height: 1.2;
        }
        
        /* --- 标题样式 --- */
        .main-title span {
            display: block;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        .main-title .line-1 {
            font-size: 60px;  
            font-weight: 700;
            color: {{TEXT_SECONDARY}};
        }
        .main-title .line-2 {
            font-size: 80px;
            color: {{TEXT_PRIMARY}};
            margin-top: 10px;
            letter-spacing: 2px;
        }

        .subtitle {
            font-size: 24px;
            font-weight: 400;
            color: rgba(249, 250, 251, 0.8);
            margin-top: 25px;
            z-index: 1;
        }

        .features-grid {
            margin-top: 40px;  
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 25px;
            z-index: 1;
        }

        /* --- 特点项目样式 --- */
        .feature-item {
            background-color: rgba(0,0,0,0.2);
            border-radius: 8px;
            padding: 25px;
            box-sizing: border-box;
            border-top: 3px solid {{FEATURE_BORDER}};
            text-align: left;
            transition: transform 0.2s ease-in-out, background-color 0.2s ease-in-out;
        }
        
        .feature-item:hover {
            transform: translateY(-5px);
            background-color: rgba(0,0,0,0.3);
        }

        /* --- SVG图标样式 --- */
        .feature-item .icon {
            color: {{TEXT_ACCENT}};
            margin-bottom: 15px;
            display: block;
        }
        .feature-item .icon svg {
            width: 38px;
            height: 38px;
        }

        .feature-item h3 {
            font-size: 20px;
            font-weight: 700;
            margin: 0 0 10px 0;
            color: #fff;
        }

        .feature-item p {
            font-size: 15px;
            font-weight: 400;
            color: rgba(249, 250, 251, 0.85);
            margin: 0;
            line-height: 1.6;
        }
        
        .footer {
            text-align: center;
            font-size: 16px;
            font-weight: 500;
            color: rgba(249, 250, 251, 0.65);
            z-index: 1;
            padding-bottom: 20px;
        }

    </style>
</head>
<body>

    <div id="cover-container">
        <div class="main-focus">
            <div class="school-name">{{UNIVERSITY}} · {{COLLEGE}}</div>
            <h1 class="main-title">
                <span class="line-1">保研夏令营 + 预推免</span>
                <span class="line-2">核心资料合集</span>
            </h1>
        </div>
        
        <div class="subtitle">
            精准定位 | 高效备战 | 全面领航
        </div>
        
        <div class="features-grid">
            <div class="feature-item">
                <div class="icon">
                    {{ICON_1}}
                </div>
                <h3>3万字真题及解析</h3>
                <p>全面覆盖经济学原理、管理学基础、数据分析等核心课程，深入分析近年面试真题与考核要点，提供专业解题思路与应对策略。</p>
            </div>
            <div class="feature-item">
                <div class="icon">
                    {{ICON_2}}
                </div>
                <h3>2025最新导师评价信息</h3>
                <p>整合全网真实学生评价与经管学院官方信息，深度分析导师研究方向、指导风格与招生偏好，助你精准匹配科技管理领域导师。</p>
            </div>
            <div class="feature-item">
                <div class="icon">
                    {{ICON_3}}
                </div>
                <h3>保研文书材料大合集</h3>
                <p>提供完整个人陈述、研究计划、简历模板与写作指南，附带高分示例与专家点评，助力文书脱颖而出。</p>
            </div>
            <div class="feature-item">
                <div class="icon">
                    {{ICON_4}}
                </div>
                <h3>核心考点配套复习资料</h3>
                <p>精炼经济学基础、信息管理、电子商务等核心考点，整合配套复习资料与习题详解，构建科技管理系统知识体系。</p>
            </div>
        </div>

        <div class="footer">
            保研喵学姐团队 倾心整理 | 2025版
        </div>
    </div>

</body>
</html>
