#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI界面模块
创建简单易用的图形用户界面
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import os
from typing import Optional, List

from .api_client import DeepSeekClient
from .excel_reader import ExcelReader
from .html_processor import HTMLProcessor
from .image_converter import ImageConverter
from .file_manager import FileManager
from config.themes import get_all_theme_names
from config.settings import APP_CONFIG


class CoverGeneratorGUI:
    """封面生成器GUI界面"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.config = APP_CONFIG
        
        # 初始化组件
        self.api_client = DeepSeekClient()
        self.excel_reader = ExcelReader()
        self.html_processor = HTMLProcessor()
        self.image_converter = ImageConverter()
        self.file_manager = FileManager()
        
        # GUI变量
        self.college_name_var = tk.StringVar()
        self.theme_var = tk.StringVar()
        self.progress_var = tk.DoubleVar()
        self.status_var = tk.StringVar(value="就绪")
        
        # 初始化界面
        self._setup_gui()
        
        # 当前处理状态
        self.is_processing = False
        self.current_batch_folder = None
    
    def _setup_gui(self):
        """设置GUI界面"""
        self.root.title(f"{self.config['app_name']} v{self.config['version']}")
        self.root.geometry("800x700")
        self.root.resizable(True, True)
        
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text=self.config['app_name'], 
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 创建选项卡
        notebook = ttk.Notebook(main_frame)
        notebook.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        main_frame.rowconfigure(1, weight=1)
        
        # 单个处理选项卡
        self._create_single_tab(notebook)
        
        # 批量处理选项卡
        self._create_batch_tab(notebook)
        
        # 设置选项卡
        self._create_settings_tab(notebook)
        
        # 进度条
        progress_frame = ttk.Frame(main_frame)
        progress_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))
        progress_frame.columnconfigure(0, weight=1)
        
        ttk.Label(progress_frame, text="进度:").grid(row=0, column=0, sticky=tk.W)
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, 
                                          maximum=100, length=400)
        self.progress_bar.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(5, 0))
        
        # 状态栏
        status_frame = ttk.Frame(main_frame)
        status_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))
        status_frame.columnconfigure(0, weight=1)
        
        self.status_label = ttk.Label(status_frame, textvariable=self.status_var)
        self.status_label.grid(row=0, column=0, sticky=tk.W)
        
        # 打开输出文件夹按钮
        ttk.Button(status_frame, text="打开输出文件夹", 
                  command=self._open_output_folder).grid(row=0, column=1, sticky=tk.E)
    
    def _create_single_tab(self, notebook):
        """创建单个处理选项卡"""
        single_frame = ttk.Frame(notebook, padding="10")
        notebook.add(single_frame, text="单个处理")
        
        # 学院名称输入
        ttk.Label(single_frame, text="学院名称:").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        college_entry = ttk.Entry(single_frame, textvariable=self.college_name_var, width=50)
        college_entry.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        single_frame.columnconfigure(0, weight=1)
        
        # 示例文本
        example_text = "示例: 电子科技大学经济与管理学院"
        ttk.Label(single_frame, text=example_text, foreground="gray").grid(
            row=2, column=0, sticky=tk.W, pady=(0, 15))
        
        # 主题选择
        ttk.Label(single_frame, text="主题色:").grid(row=3, column=0, sticky=tk.W, pady=(0, 5))
        theme_combo = ttk.Combobox(single_frame, textvariable=self.theme_var, 
                                  values=["自动选择"] + get_all_theme_names(), 
                                  state="readonly", width=20)
        theme_combo.set("自动选择")
        theme_combo.grid(row=4, column=0, sticky=tk.W, pady=(0, 15))
        
        # 按钮框架
        button_frame = ttk.Frame(single_frame)
        button_frame.grid(row=5, column=0, columnspan=2, pady=(10, 0))
        
        # 预览按钮
        ttk.Button(button_frame, text="预览效果", 
                  command=self._preview_single).pack(side=tk.LEFT, padx=(0, 10))
        
        # 生成按钮
        ttk.Button(button_frame, text="生成封面", 
                  command=self._generate_single).pack(side=tk.LEFT)
        
        # 日志区域
        ttk.Label(single_frame, text="处理日志:").grid(row=6, column=0, sticky=tk.W, pady=(20, 5))
        self.single_log = scrolledtext.ScrolledText(single_frame, height=15, width=70)
        self.single_log.grid(row=7, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        single_frame.rowconfigure(7, weight=1)
    
    def _create_batch_tab(self, notebook):
        """创建批量处理选项卡"""
        batch_frame = ttk.Frame(notebook, padding="10")
        notebook.add(batch_frame, text="批量处理")
        
        # 文件选择
        file_frame = ttk.Frame(batch_frame)
        file_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 15))
        file_frame.columnconfigure(1, weight=1)
        
        ttk.Label(file_frame, text="Excel文件:").grid(row=0, column=0, sticky=tk.W)
        self.file_path_var = tk.StringVar()
        ttk.Entry(file_frame, textvariable=self.file_path_var, width=50).grid(
            row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 10))
        ttk.Button(file_frame, text="浏览", command=self._browse_file).grid(row=0, column=2)
        
        # 文件信息
        self.file_info_var = tk.StringVar(value="请选择Excel文件")
        ttk.Label(batch_frame, textvariable=self.file_info_var, foreground="gray").grid(
            row=1, column=0, columnspan=2, sticky=tk.W, pady=(0, 15))
        
        # 批量处理选项
        options_frame = ttk.LabelFrame(batch_frame, text="处理选项", padding="10")
        options_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 15))
        
        self.auto_theme_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="自动选择主题色", 
                       variable=self.auto_theme_var).grid(row=0, column=0, sticky=tk.W)
        
        self.create_batch_folder_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="创建批量处理文件夹", 
                       variable=self.create_batch_folder_var).grid(row=1, column=0, sticky=tk.W)
        
        # 按钮
        button_frame = ttk.Frame(batch_frame)
        button_frame.grid(row=3, column=0, columnspan=2, pady=(10, 0))
        
        ttk.Button(button_frame, text="开始批量处理", 
                  command=self._start_batch_processing).pack(side=tk.LEFT, padx=(0, 10))
        
        self.stop_button = ttk.Button(button_frame, text="停止处理", 
                                     command=self._stop_processing, state="disabled")
        self.stop_button.pack(side=tk.LEFT)
        
        # 批量处理日志
        ttk.Label(batch_frame, text="批量处理日志:").grid(row=4, column=0, sticky=tk.W, pady=(20, 5))
        self.batch_log = scrolledtext.ScrolledText(batch_frame, height=15, width=70)
        self.batch_log.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        batch_frame.rowconfigure(5, weight=1)
        batch_frame.columnconfigure(0, weight=1)
    
    def _create_settings_tab(self, notebook):
        """创建设置选项卡"""
        settings_frame = ttk.Frame(notebook, padding="10")
        notebook.add(settings_frame, text="设置")
        
        # API设置
        api_frame = ttk.LabelFrame(settings_frame, text="DeepSeek API设置", padding="10")
        api_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 15))
        api_frame.columnconfigure(1, weight=1)
        
        ttk.Label(api_frame, text="API Key:").grid(row=0, column=0, sticky=tk.W)
        self.api_key_var = tk.StringVar(value=self.config['deepseek_api']['api_key'])
        ttk.Entry(api_frame, textvariable=self.api_key_var, show="*", width=40).grid(
            row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 0))
        
        ttk.Button(api_frame, text="测试连接", command=self._test_api).grid(
            row=0, column=2, padx=(10, 0))
        
        # 输出设置
        output_frame = ttk.LabelFrame(settings_frame, text="输出设置", padding="10")
        output_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 15))
        output_frame.columnconfigure(1, weight=1)
        
        ttk.Label(output_frame, text="输出目录:").grid(row=0, column=0, sticky=tk.W)
        self.output_dir_var = tk.StringVar(value=self.config['output_dir'])
        ttk.Entry(output_frame, textvariable=self.output_dir_var, width=40).grid(
            row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 10))
        ttk.Button(output_frame, text="浏览", command=self._browse_output_dir).grid(row=0, column=2)
        
        # 图片质量设置
        quality_frame = ttk.LabelFrame(settings_frame, text="图片质量设置", padding="10")
        quality_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(0, 15))
        
        ttk.Label(quality_frame, text="缩放比例:").grid(row=0, column=0, sticky=tk.W)
        self.scale_var = tk.DoubleVar(value=self.config['screenshot']['scale'])
        scale_spin = ttk.Spinbox(quality_frame, from_=1.0, to=4.0, increment=0.5, 
                                textvariable=self.scale_var, width=10)
        scale_spin.grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        
        self.auto_crop_var = tk.BooleanVar(value=self.config['screenshot']['auto_crop'])
        ttk.Checkbutton(quality_frame, text="自动裁剪白边", 
                       variable=self.auto_crop_var).grid(row=1, column=0, columnspan=2, sticky=tk.W)
        
        # 保存设置按钮
        ttk.Button(settings_frame, text="保存设置", 
                  command=self._save_settings).grid(row=3, column=0, pady=(20, 0))
        
        settings_frame.columnconfigure(0, weight=1)

    def _browse_file(self):
        """浏览Excel文件"""
        file_path = filedialog.askopenfilename(
            title="选择Excel文件",
            filetypes=[
                ("Excel文件", "*.xlsx *.xls"),
                ("CSV文件", "*.csv"),
                ("所有文件", "*.*")
            ]
        )

        if file_path:
            self.file_path_var.set(file_path)
            self._update_file_info(file_path)

    def _update_file_info(self, file_path: str):
        """更新文件信息"""
        try:
            info = self.excel_reader.get_file_info(file_path)
            if info:
                info_text = f"文件: {info['file_name']} | 行数: {info['rows']} | 列数: {info['columns']}"
                if info['first_column_sample']:
                    sample = ', '.join(info['first_column_sample'][:3])
                    info_text += f" | 示例: {sample}..."
                self.file_info_var.set(info_text)
            else:
                self.file_info_var.set("文件读取失败")
        except Exception as e:
            self.file_info_var.set(f"文件信息获取失败: {str(e)}")

    def _browse_output_dir(self):
        """浏览输出目录"""
        dir_path = filedialog.askdirectory(title="选择输出目录")
        if dir_path:
            self.output_dir_var.set(dir_path)

    def _test_api(self):
        """测试API连接"""
        def test_thread():
            try:
                self._log_message("正在测试API连接...", "single")
                # 更新API密钥
                self.api_client.config['api_key'] = self.api_key_var.get()
                self.api_client.client.api_key = self.api_key_var.get()

                success = self.api_client.test_connection()
                if success:
                    self._log_message("✓ API连接测试成功", "single")
                    messagebox.showinfo("测试成功", "DeepSeek API连接正常")
                else:
                    self._log_message("✗ API连接测试失败", "single")
                    messagebox.showerror("测试失败", "DeepSeek API连接失败，请检查API Key")
            except Exception as e:
                self._log_message(f"✗ API测试异常: {e}", "single")
                messagebox.showerror("测试异常", f"API测试过程中发生异常: {str(e)}")

        threading.Thread(target=test_thread, daemon=True).start()

    def _save_settings(self):
        """保存设置"""
        try:
            # 更新配置
            self.config['deepseek_api']['api_key'] = self.api_key_var.get()
            self.config['output_dir'] = self.output_dir_var.get()
            self.config['screenshot']['scale'] = self.scale_var.get()
            self.config['screenshot']['auto_crop'] = self.auto_crop_var.get()

            # 更新组件配置
            self.api_client.config['api_key'] = self.api_key_var.get()
            self.api_client.client.api_key = self.api_key_var.get()
            self.file_manager.output_dir = self.output_dir_var.get()
            self.image_converter.config['scale'] = self.scale_var.get()
            self.image_converter.config['auto_crop'] = self.auto_crop_var.get()

            # 确保输出目录存在
            os.makedirs(self.output_dir_var.get(), exist_ok=True)

            messagebox.showinfo("设置保存", "设置已保存成功")

        except Exception as e:
            messagebox.showerror("保存失败", f"设置保存失败: {str(e)}")

    def _preview_single(self):
        """预览单个封面"""
        college_name = self.college_name_var.get().strip()
        if not college_name:
            messagebox.showwarning("输入错误", "请输入学院名称")
            return

        def preview_thread():
            try:
                self._log_message(f"正在预览: {college_name}", "single")

                # 分析学院名称
                result = self.api_client.analyze_college_name(college_name)
                university = result['university']
                college = result['college']
                theme_name = result['theme_color']

                # 如果用户手动选择了主题，使用用户选择的
                if self.theme_var.get() != "自动选择":
                    theme_name = self.theme_var.get()

                self._log_message(f"分析结果: {university} - {college} - {theme_name}", "single")

                # 生成预览HTML
                preview_html = self.html_processor.preview_html(university, college, theme_name, 30)
                if preview_html:
                    # 显示预览窗口
                    self._show_preview_window(preview_html, university, college, theme_name)
                else:
                    self._log_message("✗ 预览生成失败", "single")

            except Exception as e:
                self._log_message(f"✗ 预览失败: {e}", "single")
                messagebox.showerror("预览失败", f"预览生成失败: {str(e)}")

        threading.Thread(target=preview_thread, daemon=True).start()

    def _show_preview_window(self, html_content: str, university: str, college: str, theme_name: str):
        """显示预览窗口"""
        preview_window = tk.Toplevel(self.root)
        preview_window.title(f"预览 - {university} {college}")
        preview_window.geometry("600x500")

        # 信息标签
        info_text = f"大学: {university}\n学院: {college}\n主题: {theme_name}"
        ttk.Label(preview_window, text=info_text, font=('Arial', 10)).pack(pady=10)

        # HTML预览
        ttk.Label(preview_window, text="HTML预览 (前30行):").pack(anchor=tk.W, padx=10)
        preview_text = scrolledtext.ScrolledText(preview_window, height=20, width=70)
        preview_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))
        preview_text.insert(tk.END, html_content)
        preview_text.config(state=tk.DISABLED)

        # 按钮
        button_frame = ttk.Frame(preview_window)
        button_frame.pack(pady=10)

        ttk.Button(button_frame, text="确认生成",
                  command=lambda: [preview_window.destroy(), self._generate_single()]).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="取消",
                  command=preview_window.destroy).pack(side=tk.LEFT, padx=5)

    def _generate_single(self):
        """生成单个封面"""
        college_name = self.college_name_var.get().strip()
        if not college_name:
            messagebox.showwarning("输入错误", "请输入学院名称")
            return

        if self.is_processing:
            messagebox.showwarning("正在处理", "请等待当前任务完成")
            return

        def generate_thread():
            try:
                self.is_processing = True
                self._update_progress(0, "开始处理...")

                # 分析学院名称
                self._log_message(f"正在分析学院名称: {college_name}", "single")
                result = self.api_client.analyze_college_name(college_name)
                university = result['university']
                college = result['college']
                theme_name = result['theme_color']

                # 如果用户手动选择了主题，使用用户选择的
                if self.theme_var.get() != "自动选择":
                    theme_name = self.theme_var.get()

                self._log_message(f"分析结果: {university} - {college} - {theme_name}", "single")
                self._update_progress(20, "分析完成，正在生成HTML...")

                # 获取输出路径
                paths = self.file_manager.get_output_paths(university, college)
                self._log_message(f"输出路径: {paths['folder']}", "single")

                # 生成HTML
                success = self.html_processor.generate_and_save(
                    university, college, theme_name, paths['html'])

                if not success:
                    raise Exception("HTML生成失败")

                self._log_message(f"✓ HTML文件生成成功: {paths['html']}", "single")
                self._update_progress(50, "HTML生成完成，正在转换为图片...")

                # 转换为图片
                def progress_callback(progress, message):
                    # 将图片转换进度映射到50-100范围
                    mapped_progress = 50 + (progress * 0.5)
                    self._update_progress(mapped_progress, f"图片转换: {message}")

                def success_callback(output_path):
                    self._log_message(f"✓ 图片生成成功: {output_path}", "single")
                    self._update_progress(100, "处理完成")

                    # 询问是否打开文件夹
                    if messagebox.askyesno("处理完成", f"封面生成完成！\n\n是否打开输出文件夹？"):
                        self.file_manager.open_folder(paths['folder'])

                def error_callback(error_msg):
                    self._log_message(f"✗ 图片生成失败: {error_msg}", "single")
                    self._update_progress(0, "处理失败")
                    messagebox.showerror("处理失败", f"图片生成失败: {error_msg}")

                # 开始图片转换
                self.image_converter.convert_html_to_image(
                    paths['html'], paths['image'],
                    progress_callback, success_callback, error_callback
                )

            except Exception as e:
                self._log_message(f"✗ 处理失败: {e}", "single")
                self._update_progress(0, "处理失败")
                messagebox.showerror("处理失败", f"封面生成失败: {str(e)}")
            finally:
                self.is_processing = False

        threading.Thread(target=generate_thread, daemon=True).start()

    def _start_batch_processing(self):
        """开始批量处理"""
        file_path = self.file_path_var.get().strip()
        if not file_path or not os.path.exists(file_path):
            messagebox.showwarning("文件错误", "请选择有效的Excel文件")
            return

        if self.is_processing:
            messagebox.showwarning("正在处理", "请等待当前任务完成")
            return

        def batch_thread():
            try:
                self.is_processing = True
                self.stop_button.config(state="normal")
                self._update_progress(0, "开始批量处理...")

                # 读取Excel文件
                self._log_message(f"正在读取文件: {file_path}", "batch")
                college_names = self.excel_reader.read_college_names(file_path)

                if not college_names:
                    raise Exception("未能从文件中读取到学院名称")

                self._log_message(f"读取到 {len(college_names)} 个学院名称", "batch")

                # 创建批量处理文件夹
                if self.create_batch_folder_var.get():
                    self.current_batch_folder = self.file_manager.create_batch_folder()
                    self._log_message(f"创建批量文件夹: {self.current_batch_folder}", "batch")

                # 处理每个学院
                total_count = len(college_names)
                success_count = 0

                for i, college_name in enumerate(college_names):
                    if not self.is_processing:  # 检查是否被停止
                        break

                    try:
                        self._log_message(f"[{i+1}/{total_count}] 处理: {college_name}", "batch")

                        # 分析学院名称
                        result = self.api_client.analyze_college_name(college_name)
                        university = result['university']
                        college = result['college']
                        theme_name = result['theme_color']

                        # 获取输出路径
                        if self.current_batch_folder:
                            paths = self.file_manager.get_batch_output_paths(
                                university, college, self.current_batch_folder)
                        else:
                            paths = self.file_manager.get_output_paths(university, college)

                        # 生成HTML
                        html_success = self.html_processor.generate_and_save(
                            university, college, theme_name, paths['html'])

                        if html_success:
                            self._log_message(f"  ✓ HTML生成成功", "batch")

                            # 转换为图片（同步方式）
                            img_success = self.image_converter._do_conversion(
                                paths['html'], paths['image'])

                            if img_success:
                                self._log_message(f"  ✓ 图片生成成功", "batch")
                                success_count += 1
                            else:
                                self._log_message(f"  ✗ 图片生成失败", "batch")
                        else:
                            self._log_message(f"  ✗ HTML生成失败", "batch")

                        # 更新进度
                        progress = ((i + 1) / total_count) * 100
                        self._update_progress(progress, f"已处理 {i+1}/{total_count}")

                    except Exception as e:
                        self._log_message(f"  ✗ 处理失败: {e}", "batch")

                # 完成处理
                if self.is_processing:  # 如果没有被停止
                    self._log_message(f"批量处理完成！成功: {success_count}/{total_count}", "batch")
                    self._update_progress(100, "批量处理完成")

                    # 询问是否打开文件夹
                    folder_to_open = self.current_batch_folder or self.file_manager.output_dir
                    if messagebox.askyesno("处理完成",
                                         f"批量处理完成！\n成功处理: {success_count}/{total_count}\n\n是否打开输出文件夹？"):
                        self.file_manager.open_folder(folder_to_open)
                else:
                    self._log_message("批量处理已停止", "batch")
                    self._update_progress(0, "处理已停止")

            except Exception as e:
                self._log_message(f"✗ 批量处理失败: {e}", "batch")
                self._update_progress(0, "处理失败")
                messagebox.showerror("处理失败", f"批量处理失败: {str(e)}")
            finally:
                self.is_processing = False
                self.stop_button.config(state="disabled")
                self.current_batch_folder = None

        threading.Thread(target=batch_thread, daemon=True).start()

    def _stop_processing(self):
        """停止处理"""
        if self.is_processing:
            self.is_processing = False
            self._log_message("正在停止处理...", "batch")
            self._update_progress(0, "正在停止...")

    def _open_output_folder(self):
        """打开输出文件夹"""
        self.file_manager.open_folder(self.file_manager.output_dir)

    def _update_progress(self, value: float, message: str = ""):
        """更新进度条和状态"""
        self.progress_var.set(value)
        if message:
            self.status_var.set(message)
        self.root.update_idletasks()

    def _log_message(self, message: str, log_type: str = "single"):
        """记录日志消息"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        if log_type == "single":
            self.single_log.insert(tk.END, log_entry)
            self.single_log.see(tk.END)
        elif log_type == "batch":
            self.batch_log.insert(tk.END, log_entry)
            self.batch_log.see(tk.END)

        self.root.update_idletasks()

    def run(self):
        """运行GUI应用"""
        try:
            # 初始化检查
            self._log_message("程序启动", "single")
            self._log_message("程序启动", "batch")

            # 检查模板
            if not self.html_processor.validate_template():
                messagebox.showwarning("模板警告", "HTML模板验证失败，可能影响生成效果")

            # 运行主循环
            self.root.mainloop()

        except Exception as e:
            messagebox.showerror("程序错误", f"程序运行时发生错误: {str(e)}")
            raise


def main():
    """GUI模块测试入口"""
    try:
        app = CoverGeneratorGUI()
        app.run()
    except Exception as e:
        print(f"GUI启动失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
