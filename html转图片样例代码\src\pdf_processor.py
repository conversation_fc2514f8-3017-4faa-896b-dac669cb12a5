"""
PDF处理模块 - 用于裁剪PDF白边
支持自动检测和裁剪PDF文件的白边区域
"""

import os
import time
import tempfile
import shutil
try:
    from PIL import Image
    import fitz  # PyMuPDF
    import numpy as np
    DEPENDENCIES_AVAILABLE = True
except ImportError as e:
    print(f"PDF处理依赖缺失: {e}")
    DEPENDENCIES_AVAILABLE = False


class PDFProcessor:
    """PDF处理器 - 专门用于裁剪PDF白边"""
    
    def __init__(self):
        if not DEPENDENCIES_AVAILABLE:
            raise ImportError("PDF处理依赖未安装，请运行 install_dependencies.bat")
    
    def crop_pdf_whitespace(self, pdf_path, output_path=None, margin=10):
        """
        裁剪PDF文件的白边
        
        Args:
            pdf_path: 输入PDF文件路径
            output_path: 输出PDF文件路径，如果为None则覆盖原文件
            margin: 保留的边距（像素）
        
        Returns:
            bool: 是否成功裁剪
        """
        if not os.path.exists(pdf_path):
            print(f"PDF文件不存在: {pdf_path}")
            return False
        
        print(f"开始智能裁剪PDF白边: {pdf_path}")
        
        # 创建临时输出文件
        temp_output = None
        pdf_document = None
        new_pdf = None
        
        try:
            # 等待文件完全写入并释放
            time.sleep(2)
            
            # 使用临时文件避免文件占用问题
            temp_output = pdf_path + ".temp_" + str(int(time.time()))
            
            # 多次重试打开PDF文件
            for attempt in range(5):
                try:
                    print(f"尝试打开PDF文件 (第{attempt+1}次)...")
                    pdf_document = fitz.open(pdf_path)
                    break
                except Exception as e:
                    print(f"打开PDF失败: {e}")
                    if attempt < 4:
                        time.sleep(1)
                    else:
                        raise
            
            if pdf_document is None or len(pdf_document) == 0:
                print("PDF文件为空或无法打开")
                return False
            
            # 获取第一页进行分析
            page = pdf_document[0]
            page_rect = page.rect
            print(f"原始页面尺寸: {page_rect.width:.1f}x{page_rect.height:.1f}")
            
            # 转换为高分辨率图像进行分析
            print("正在转换PDF为图像进行内容分析...")
            mat = fitz.Matrix(2.0, 2.0)  # 2倍放大提高精度
            pix = page.get_pixmap(matrix=mat)
            img_data = pix.tobytes("png")
            pix = None  # 释放内存
            
            # 创建临时图像文件用于分析
            temp_img_fd, temp_img_path = tempfile.mkstemp(suffix='.png')
            try:
                with os.fdopen(temp_img_fd, 'wb') as f:
                    f.write(img_data)
                
                # 检测内容边界
                print("正在智能检测内容边界...")
                content_bounds = self._detect_content_bounds_advanced(temp_img_path, margin)
                
                if not content_bounds:
                    print("未检测到明显的内容边界，保持原样")
                    return True
                
                # 转换坐标回PDF空间
                x1, y1, x2, y2 = content_bounds
                scale = 2.0  # 对应上面的放大倍数
                
                pdf_x1 = x1 / scale
                pdf_y1 = y1 / scale
                pdf_x2 = x2 / scale
                pdf_y2 = y2 / scale
                
                # 确保边界在页面范围内
                pdf_x1 = max(0, pdf_x1)
                pdf_y1 = max(0, pdf_y1)
                pdf_x2 = min(page_rect.width, pdf_x2)
                pdf_y2 = min(page_rect.height, pdf_y2)
                
                print(f"检测到的内容区域: ({pdf_x1:.1f}, {pdf_y1:.1f}, {pdf_x2:.1f}, {pdf_y2:.1f})")
                
                # 检查是否需要裁剪
                crop_width = pdf_x2 - pdf_x1
                crop_height = pdf_y2 - pdf_y1
                
                # 验证裁剪区域是否合理
                if crop_width < page_rect.width * 0.3 or crop_height < page_rect.height * 0.3:
                    print("检测到的内容区域过小，可能检测有误，保持原样")
                    return True
                
                # 计算白边大小
                left_margin = pdf_x1
                top_margin = pdf_y1
                right_margin = page_rect.width - pdf_x2
                bottom_margin = page_rect.height - pdf_y2
                
                max_margin = max(left_margin, top_margin, right_margin, bottom_margin)
                
                # 如果白边很小就不裁剪
                if max_margin < 15:  # 小于15像素的白边不处理
                    print(f"白边已经很小 (最大{max_margin:.1f}像素)，无需裁剪")
                    return True
                
                print(f"白边分析: 左{left_margin:.1f} 上{top_margin:.1f} 右{right_margin:.1f} 下{bottom_margin:.1f}")
                print("开始执行PDF裁剪...")
                
                # 创建新PDF文档
                new_pdf = fitz.open()
                
                # 设置裁剪区域
                crop_rect = fitz.Rect(pdf_x1, pdf_y1, pdf_x2, pdf_y2)
                
                # 处理所有页面（不只是第一页）
                for page_num in range(len(pdf_document)):
                    source_page = pdf_document[page_num]
                    
                    # 创建新页面
                    new_page = new_pdf.new_page(width=crop_width, height=crop_height)
                    
                    # 将源页面内容复制到新页面的裁剪区域
                    new_page.show_pdf_page(new_page.rect, pdf_document, page_num, clip=crop_rect)
                
                # 先关闭原PDF文档释放文件句柄
                pdf_document.close()
                pdf_document = None
                
                # 保存新PDF到临时文件
                new_pdf.save(temp_output)
                new_pdf.close()
                new_pdf = None
                
                print("PDF裁剪处理完成，正在替换原文件...")
                
                # 确保原文件已释放
                time.sleep(1)
                
                # 使用atomic操作替换文件
                final_output = output_path if output_path else pdf_path
                
                # 备份原文件
                backup_path = pdf_path + ".backup"
                if os.path.exists(pdf_path):
                    shutil.copy2(pdf_path, backup_path)
                
                # 替换文件
                try:
                    if os.path.exists(final_output):
                        os.remove(final_output)
                    shutil.move(temp_output, final_output)
                    
                    # 删除备份
                    if os.path.exists(backup_path):
                        os.remove(backup_path)
                    
                    print(f"✓ PDF白边裁剪完成！")
                    print(f"裁剪后尺寸: {crop_width:.1f}x{crop_height:.1f}")
                    print(f"节省空间: 宽度{page_rect.width-crop_width:.1f} 高度{page_rect.height-crop_height:.1f}")
                    return True
                    
                except Exception as e:
                    # 恢复备份
                    if os.path.exists(backup_path):
                        shutil.copy2(backup_path, pdf_path)
                        os.remove(backup_path)
                    raise e
                    
            finally:
                # 清理临时图像文件
                try:
                    if os.path.exists(temp_img_path):
                        os.unlink(temp_img_path)
                except:
                    pass
                    
        except Exception as e:
            print(f"PDF裁剪失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
            
        finally:
            # 确保所有资源都被释放
            try:
                if pdf_document:
                    pdf_document.close()
            except:
                pass
            
            try:
                if new_pdf:
                    new_pdf.close()
            except:
                pass
            
            # 清理临时文件
            try:
                if temp_output and os.path.exists(temp_output):
                    os.remove(temp_output)
            except:
                pass
    
    def _detect_content_bounds_advanced(self, image_path, margin=10):
        """
        高级内容边界检测 - 更智能的白边识别
        
        Args:
            image_path: 图像文件路径
            margin: 边距
        
        Returns:
            tuple: (x1, y1, x2, y2) 内容区域坐标
        """
        try:
            with Image.open(image_path) as img:
                # 转换为RGB模式确保兼容性
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                img_array = np.array(img)
                
                # 转换为灰度
                if len(img_array.shape) == 3:
                    gray = np.mean(img_array, axis=2)
                else:
                    gray = img_array
                
                print(f"图像尺寸: {img.width}x{img.height}")
                
                # 多层次白边检测策略
                thresholds = [252, 245, 235, 220]  # 从严格到宽松
                best_bounds = None
                
                for i, threshold in enumerate(thresholds):
                    print(f"尝试阈值 {threshold} (第{i+1}/{len(thresholds)}次)...")
                    
                    # 检测非白色区域
                    content_mask = gray < threshold
                    
                    # 检查是否有内容
                    if not np.any(content_mask):
                        print(f"阈值{threshold}: 未检测到内容")
                        continue
                    
                    # 找到内容区域的边界
                    content_rows = np.any(content_mask, axis=1)
                    content_cols = np.any(content_mask, axis=0)
                    
                    if np.any(content_rows) and np.any(content_cols):
                        top = np.argmax(content_rows)
                        bottom = len(content_rows) - np.argmax(content_rows[::-1]) - 1
                        left = np.argmax(content_cols)
                        right = len(content_cols) - np.argmax(content_cols[::-1]) - 1
                        
                        # 计算内容区域大小和占比
                        content_width = right - left + 1
                        content_height = bottom - top + 1
                        total_area = img.width * img.height
                        content_area = content_width * content_height
                        content_ratio = content_area / total_area
                        
                        print(f"阈值{threshold}: 内容区域 {content_width}x{content_height}, 占比{content_ratio:.1%}")
                        
                        # 检查内容区域是否合理
                        # 1. 占比要在合理范围内
                        # 2. 不能太窄或太矮
                        min_ratio = 0.15 if i < 2 else 0.1  # 前两个阈值要求更严格
                        max_ratio = 0.98
                        
                        min_size_ratio = 0.3  # 最小尺寸占比
                        
                        if (min_ratio <= content_ratio <= max_ratio and
                            content_width >= img.width * min_size_ratio and
                            content_height >= img.height * min_size_ratio):
                            
                            # 添加边距，但不超出图像边界
                            x1 = max(0, left - margin)
                            y1 = max(0, top - margin)
                            x2 = min(img.width - 1, right + margin)
                            y2 = min(img.height - 1, bottom + margin)
                            
                            best_bounds = (x1, y1, x2, y2)
                            
                            print(f"✓ 使用阈值{threshold}检测成功!")
                            print(f"原始边界: left={left}, top={top}, right={right}, bottom={bottom}")
                            print(f"加边距后: ({x1}, {y1}, {x2}, {y2})")
                            break
                        else:
                            print(f"阈值{threshold}: 内容区域不合理 (占比{content_ratio:.1%})")
                
                # 如果标准方法都没找到合适的边界，尝试边缘检测
                if best_bounds is None:
                    print("标准检测未成功，尝试边缘检测...")
                    best_bounds = self._detect_by_edge_analysis(img_array, margin)
                
                if best_bounds:
                    print(f"最终检测结果: {best_bounds}")
                else:
                    print("所有检测方法都未成功")
                
                return best_bounds
                
        except Exception as e:
            print(f"高级内容检测失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return None
    
    def _detect_by_edge_analysis(self, img_array, margin=10):
        """
        基于边缘检测的内容区域识别
        """
        try:
            # 转换为灰度
            if len(img_array.shape) == 3:
                gray = np.mean(img_array, axis=2)
            else:
                gray = img_array
            
            height, width = gray.shape
            
            # 计算图像梯度来检测边缘
            grad_x = np.abs(np.diff(gray, axis=1))
            grad_y = np.abs(np.diff(gray, axis=0))
            
            # 计算每行和每列的活动强度
            row_activity = np.mean(grad_y, axis=1)
            col_activity = np.mean(grad_x, axis=0)
            
            # 动态设定阈值
            row_threshold = np.percentile(row_activity, 75)  # 75%分位数
            col_threshold = np.percentile(col_activity, 75)
            
            # 找到有活动的区域
            active_rows = row_activity > row_threshold
            active_cols = col_activity > col_threshold
            
            if np.any(active_rows) and np.any(active_cols):
                # 找到连续的活动区域
                active_row_indices = np.where(active_rows)[0]
                active_col_indices = np.where(active_cols)[0]
                
                # 使用最外层的活动区域
                top = active_row_indices[0]
                bottom = active_row_indices[-1]
                left = active_col_indices[0]
                right = active_col_indices[-1]
                
                # 适当扩展边界
                expand = min(width, height) // 30  # 扩展约3%
                
                x1 = max(0, left - expand - margin)
                y1 = max(0, top - expand - margin)
                x2 = min(width - 1, right + expand + margin)
                y2 = min(height - 1, bottom + expand + margin)
                
                # 验证结果
                content_width = x2 - x1
                content_height = y2 - y1
                content_ratio = (content_width * content_height) / (width * height)
                
                if content_ratio > 0.1:  # 至少要有10%的内容
                    print(f"边缘检测成功: ({x1}, {y1}, {x2}, {y2}), 占比{content_ratio:.1%}")
                    return (x1, y1, x2, y2)
            
            print("边缘检测未找到合适的内容区域")
            return None
            
        except Exception as e:
            print(f"边缘检测失败: {str(e)}")
            return None
    
    def get_pdf_info(self, pdf_path):
        """
        获取PDF文件信息
        
        Args:
            pdf_path: PDF文件路径
        
        Returns:
            dict: PDF信息
        """
        try:
            pdf_document = fitz.open(pdf_path)
            
            info = {
                'page_count': len(pdf_document),
                'title': pdf_document.metadata.get('title', ''),
                'author': pdf_document.metadata.get('author', ''),
                'pages': []
            }
            
            for page_num in range(len(pdf_document)):
                page = pdf_document[page_num]
                page_info = {
                    'page_number': page_num + 1,
                    'width': page.rect.width,
                    'height': page.rect.height
                }
                info['pages'].append(page_info)
            
            pdf_document.close()
            return info
            
        except Exception as e:
            print(f"获取PDF信息失败: {str(e)}")
            return None


# 测试函数
if __name__ == "__main__":
    processor = PDFProcessor()
    
    # 测试PDF信息获取
    test_pdf = r"test.pdf"
    if os.path.exists(test_pdf):
        info = processor.get_pdf_info(test_pdf)
        print("PDF信息:", info)
        
        # 测试白边裁剪
        success = processor.crop_pdf_whitespace(test_pdf)
        print("裁剪结果:", success)
