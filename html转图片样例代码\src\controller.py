"""
HTML转换工具 - 主控制器
协调各个模块的工作，支持进度回调
支持HTML转图片和PDF
"""

from .screenshot import ScreenshotCapture
from .image_processor import ImageProcessor
from .config import DEFAULT_CONFIG


class ScreenshotHandler:
    """HTML转换处理控制器（支持图片和PDF）"""
    
    def __init__(self, config=None):
        self.config = config or DEFAULT_CONFIG
        self.screenshot_capture = ScreenshotCapture(self.config)
        self.image_processor = ImageProcessor(
            crop_threshold=self.config.get('crop_threshold', 240),
            crop_padding=self.config.get('crop_padding', 20)
        )
    
    def process_screenshot(self, html_path, save_path, quality, format_type, 
                         screenshot_type, enable_vector, anti_aliasing, auto_crop,
                         success_callback=None, error_callback=None, progress_callback=None):
        """
        处理HTML转换请求（图片或PDF）
        
        Args:
            html_path: HTML文件路径
            save_path: 保存路径
            quality: 质量等级
            format_type: 输出格式（PNG/JPEG/WEBP/PDF等）
            screenshot_type: 截图类型（仅对图片有效）
            enable_vector: 启用矢量优化
            anti_aliasing: 启用抗锯齿
            auto_crop: 自动裁剪白边（仅对图片有效）
            success_callback: 成功回调
            error_callback: 错误回调
            progress_callback: 进度回调
        """
        try:
            # 获取质量设置
            quality_settings = self._get_quality_settings(quality)
            
            # 定义成功回调
            def on_success(save_path):
                print(f"✓ 转换成功: {save_path}")
                if success_callback:
                    success_callback(save_path)
            
            # 定义错误回调
            def on_error(error_msg):
                print(f"✗ 转换失败: {error_msg}")
                if error_callback:
                    error_callback(error_msg)
            
            # 定义进度回调
            def on_progress(progress, message=""):
                if progress_callback:
                    progress_callback(progress, message)
            
            # 区分PDF和图片处理
            if format_type == "PDF":
                print(f"开始HTML转PDF转换: {html_path}")
            else:
                print(f"开始HTML转{format_type}转换: {html_path}")
            
            # 开始转换
            self.screenshot_capture.capture_html_screenshot(
                html_path=html_path,
                save_path=save_path,
                quality_settings=quality_settings,
                format_type=format_type,
                screenshot_type=screenshot_type,
                enable_vector=enable_vector,
                anti_aliasing=anti_aliasing,
                auto_crop=auto_crop,
                callback=on_success,
                error_callback=on_error,
                progress_callback=on_progress
            )
            
        except Exception as e:
            error_msg = f"转换控制器错误: {str(e)}"
            print(error_msg)
            if error_callback:
                error_callback(error_msg)
    
    def _get_quality_settings(self, quality):
        """获取质量设置"""
        quality_map = {
            "标准": "标准清晰度",
            "高清": "高清(HD)", 
            "全高清": "全高清(FHD)",
            "超高清(4K)": "超高清(4K)",
            "8K": "8K超清",
            "矢量": "矢量级别"
        }
        
        quality_key = quality_map.get(quality, "超高清(4K)")
        return self.config['quality_settings'][quality_key]
