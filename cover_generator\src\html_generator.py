"""
HTML模板生成器
根据学校信息和主题色生成HTML封面
"""

import os
import random
from typing import Dict
from .config import THEME_COLORS, ICON_SETS, get_template_path

class HTMLGenerator:
    def __init__(self):
        self.base_template = self._load_base_template()
    
    def _load_base_template(self) -> str:
        """
        加载基础HTML模板
        """
        # 从原始封面设计样例.html文件加载模板
        template_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "封面设计样例.html")
        
        try:
            with open(template_path, 'r', encoding='utf-8') as f:
                return f.read()
        except FileNotFoundError:
            print(f"模板文件未找到: {template_path}")
            # 返回一个简化的默认模板
            return self._get_default_template()
    
    def _get_default_template(self) -> str:
        """
        默认HTML模板
        """
        return '''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产品封面生成器 - {school_full_name}</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700;900&display=swap');
        
        body {{
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f8fafc;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            padding: 20px;
            margin: 0;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }}

        #cover-container {{
            width: 750px;
            height: 1000px;
            background: linear-gradient(145deg, {primary_color} 0%, {secondary_color} 100%);
            color: {text_color};
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            padding: 50px;
            box-sizing: border-box;
            border-radius: 8px;
            overflow: hidden;
            position: relative;
            text-align: center;
            box-shadow: 0 15px 30px rgba(0,0,0,0.15);
        }}

        #cover-container::before {{
            content: '';
            position: absolute;
            top: -150px;
            left: -200px;
            width: 600px;
            height: 600px;
            background: radial-gradient(circle, {accent_color_rgba_12} 0%, {accent_color_rgba_0} 70%);
            transform: rotate(45deg);
        }}
        
        #cover-container::after {{
            content: '';
            position: absolute;
            bottom: -200px;
            right: -200px;
            width: 550px;
            height: 550px;
            background: radial-gradient(circle, {accent_color_rgba_8} 0%, {accent_color_rgba_0} 70%);
            border-radius: 50%;
        }}

        .main-focus {{
            z-index: 1;
            margin-top: 40px;
        }}
        
        .main-focus .school-name {{
            font-size: 28px;
            font-weight: 500;
            background-color: rgba(0, 0, 0, 0.2);
            color: {accent_color};
            padding: 12px 35px;
            border-radius: 50px;
            display: inline-block;
            margin-bottom: 30px;
            border: 1px solid {accent_color_rgba_30};
        }}

        .main-focus .main-title {{
            font-weight: 900;
            text-shadow: none;
            color: #FFFFFF;
            margin: 0;
            line-height: 1.2;
        }}
        
        .main-title span {{
            display: block;
            text-transform: uppercase;
            letter-spacing: 1px;
        }}
        .main-title .line-1 {{
            font-size: 60px;  
            font-weight: 700;
            color: #dbeafe;
        }}
        .main-title .line-2 {{
            font-size: 80px;
            color: #FFFFFF;
            margin-top: 10px;
            letter-spacing: 2px;
        }}

        .subtitle {{
            font-size: 24px;
            font-weight: 400;
            color: rgba(249, 250, 251, 0.8);
            margin-top: 25px;
            z-index: 1;
        }}

        .features-grid {{
            margin-top: 40px;  
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 25px;
            z-index: 1;
        }}

        .feature-item {{
            background-color: rgba(0,0,0,0.2);
            border-radius: 8px;
            padding: 25px;
            box-sizing: border-box;
            border-top: 3px solid {accent_color_rgba_60};
            text-align: left;
            transition: transform 0.2s ease-in-out, background-color 0.2s ease-in-out;
        }}
        
        .feature-item:hover {{
            transform: translateY(-5px);
            background-color: rgba(0,0,0,0.3);
        }}

        .feature-item .icon {{
            color: {accent_color};
            margin-bottom: 15px;
            display: block;
        }}
        .feature-item .icon svg {{
            width: 38px;
            height: 38px;
        }}

        .feature-item h3 {{
            font-size: 20px;
            font-weight: 700;
            margin: 0 0 10px 0;
            color: #fff;
        }}

        .feature-item p {{
            font-size: 15px;
            font-weight: 400;
            color: rgba(249, 250, 251, 0.85);
            margin: 0;
            line-height: 1.6;
        }}
        
        .footer {{
            text-align: center;
            font-size: 16px;
            font-weight: 500;
            color: rgba(249, 250, 251, 0.65);
            z-index: 1;
            padding-bottom: 20px;
        }}
    </style>
</head>
<body>
    <div id="cover-container">
        <div class="main-focus">
            <div class="school-name">{school_full_name}</div>
            <h1 class="main-title">
                <span class="line-1">保研夏令营 + 预推免</span>
                <span class="line-2">核心资料合集</span>
            </h1>
        </div>
        
        <div class="subtitle">
            精准定位 | 高效备战 | 全面领航
        </div>
        
        <div class="features-grid">
            {features_html}
        </div>

        <div class="footer">
            保研喵学姐团队 倾心整理 | 2025版
        </div>
    </div>
</body>
</html>'''
    
    def generate_html(self, school_info: Dict) -> str:
        """
        根据学校信息生成HTML
        
        Args:
            school_info: {
                "university": "大学名",
                "college": "学院名", 
                "theme_color": "主题色名称"
            }
        
        Returns:
            str: 生成的HTML内容
        """
        # 获取主题色配置
        theme = THEME_COLORS.get(school_info["theme_color"], THEME_COLORS["科技蓝"])
        
        # 构建学校全名
        university = school_info["university"]
        college = school_info["college"]
        if college and college != "未知学院":
            school_full_name = f"{university} · {college}"
        else:
            school_full_name = university
        
        # 生成特色功能HTML
        features_html = self._generate_features_html(college, theme["accent"])
        
        # 准备模板变量
        template_vars = {
            "school_full_name": school_full_name,
            "primary_color": theme["primary"],
            "secondary_color": theme["secondary"],
            "accent_color": theme["accent"],
            "text_color": theme["text"],
            "accent_color_rgba_12": self._hex_to_rgba(theme["accent"], 0.12),
            "accent_color_rgba_8": self._hex_to_rgba(theme["accent"], 0.08),
            "accent_color_rgba_0": self._hex_to_rgba(theme["accent"], 0),
            "accent_color_rgba_30": self._hex_to_rgba(theme["accent"], 0.3),
            "accent_color_rgba_60": self._hex_to_rgba(theme["accent"], 0.6),
            "features_html": features_html
        }
        
        # 使用基础模板或默认模板
        template = self.base_template
        
        # 如果基础模板中没有占位符，使用默认模板
        if "{school_full_name}" not in template:
            template = self._get_default_template()
        
        # 替换模板变量
        html_content = template.format(**template_vars)
        
        return html_content
    
    def _generate_features_html(self, college: str, accent_color: str) -> str:
        """
        根据学院类型生成特色功能HTML
        """
        # 选择合适的图标集合
        selected_icons = self._select_icons_for_college(college)
        
        # 特色功能内容
        features = [
            {
                "title": "3万字真题及解析",
                "content": f"全面覆盖{college}核心课程，深入分析近年面试真题与考核要点，提供专业解题思路与应对策略。"
            },
            {
                "title": "2025最新导师评价信息", 
                "content": f"整合全网真实学生评价与{college}官方信息，深度分析导师研究方向、指导风格与招生偏好。"
            },
            {
                "title": "保研文书材料大合集",
                "content": "提供完整个人陈述、研究计划、简历模板与写作指南，附带高分示例与专家点评，助力文书脱颖而出。"
            },
            {
                "title": "核心考点配套复习资料",
                "content": f"精炼{college}专业核心考点，整合配套复习资料与习题详解，构建系统知识体系。"
            }
        ]
        
        features_html = ""
        for i, feature in enumerate(features):
            icon_svg = selected_icons[i % len(selected_icons)]
            features_html += f'''
            <div class="feature-item">
                <div class="icon">
                    {icon_svg}
                </div>
                <h3>{feature["title"]}</h3>
                <p>{feature["content"]}</p>
            </div>'''
        
        return features_html
    
    def _select_icons_for_college(self, college: str) -> list:
        """
        根据学院名称选择合适的图标
        """
        # 关键词匹配图标
        college_lower = college.lower()
        matched_icons = []
        
        for icon_name, icon_data in ICON_SETS.items():
            keywords = icon_data["keywords"]
            if any(keyword in college_lower for keyword in keywords):
                matched_icons.append(icon_data["icon"])
        
        # 如果没有匹配的图标，使用默认图标集
        if not matched_icons:
            matched_icons = [icon_data["icon"] for icon_data in ICON_SETS.values()]
        
        # 随机排序并确保有4个图标
        random.shuffle(matched_icons)
        while len(matched_icons) < 4:
            matched_icons.extend(matched_icons)
        
        return matched_icons[:4]
    
    def _hex_to_rgba(self, hex_color: str, alpha: float) -> str:
        """
        将十六进制颜色转换为rgba格式
        """
        # 移除#号
        hex_color = hex_color.lstrip('#')
        
        # 转换为RGB
        try:
            r = int(hex_color[0:2], 16)
            g = int(hex_color[2:4], 16)
            b = int(hex_color[4:6], 16)
            return f"rgba({r}, {g}, {b}, {alpha})"
        except:
            # 如果转换失败，返回透明色
            return f"rgba(0, 0, 0, {alpha})"

# 创建全局实例
html_generator = HTMLGenerator()
