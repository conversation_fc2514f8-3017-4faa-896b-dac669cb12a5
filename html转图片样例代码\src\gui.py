"""
HTML转图片工具 - 简约界面
简约朴素的界面设计，带进度条显示
"""

import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import os
import threading
import datetime
from .config import DEFAULT_CONFIG


class MinimalGUI:
    """简约界面 - 带进度条显示"""
    
    def __init__(self, screenshot_handler=None):
        self.root = tk.Tk()
        self.screenshot_handler = screenshot_handler
          # 状态变量
        self.current_html_file = None
        self.is_converting = False
        
        # UI变量
        self.file_path_var = tk.StringVar()
        self.quality_var = tk.StringVar(value="超高清(4K)")
        self.format_var = tk.StringVar(value="PNG")
        self.auto_crop_var = tk.BooleanVar(value=True)
        self.status_var = tk.StringVar(value="就绪")
        self.progress_var = tk.DoubleVar()
        
        self.setup_window()
        self.create_ui()
    
    def setup_window(self):
        """设置窗口"""
        self.root.title("HTML转图片工具")
        self.root.geometry("700x650")
        self.root.configure(bg='white')
        self.root.resizable(True, True)
        # 设置最小窗口尺寸，确保界面不会被压缩过小
        self.root.minsize(650, 600)
        
        # 居中窗口
        self.center_window()
    
    def center_window(self):
        """窗口居中"""
        self.root.update_idletasks()
        w = 700
        h = 650
        
        sw = self.root.winfo_screenwidth()
        sh = self.root.winfo_screenheight()
        
        x = (sw - w) // 2
        y = (sh - h) // 2
        
        self.root.geometry(f"{w}x{h}+{x}+{y}")
    
    def create_ui(self):
        """创建用户界面"""
        # 主框架
        main_frame = tk.Frame(self.root, bg='white', padx=20, pady=15)
        main_frame.pack(fill='both', expand=True)
        
        # 标题
        title_label = tk.Label(
            main_frame,
            text="HTML转图片工具",
            font=('微软雅黑', 16, 'bold'),
            bg='white',
            fg='#333333'
        )
        title_label.pack(pady=(0, 15))
        
        # 文件选择区域
        self.create_file_section(main_frame)
          # 设置区域
        self.create_settings_section(main_frame)
        
        # 转换按钮
        self.convert_btn = tk.Button(
            main_frame,
            text="开始转换",
            font=('微软雅黑', 12, 'bold'),
            bg='#0078d4',
            fg='white',
            relief='flat',
            cursor='hand2',
            command=self.save_as_image,
            pady=8
        )
        self.convert_btn.pack(pady=(10, 10))
          # 添加悬停效果
        def on_convert_enter(e):
            if not self.is_converting:
                self.convert_btn.configure(bg='#106ebe')
        def on_convert_leave(e):
            if not self.is_converting:
                self.convert_btn.configure(bg='#0078d4')
        
        self.convert_btn.bind("<Enter>", on_convert_enter)
        self.convert_btn.bind("<Leave>", on_convert_leave)
        
        # 进度条区域（在终端输出上方）
        self.create_progress_section(main_frame)
        
        # 终端输出区域（紧凑布局）
        self.create_terminal_section(main_frame)
        
        # 状态栏
        status_frame = tk.Frame(main_frame, bg='#f5f5f5', relief='solid', bd=1)
        status_frame.pack(fill='x', pady=(3, 0))
        
        self.status_label = tk.Label(
            status_frame,
            textvariable=self.status_var,
            font=('微软雅黑', 9),
            bg='#f5f5f5',
            fg='#666666'
        )
        self.status_label.pack(pady=3)
    
    def create_file_section(self, parent):
        """创建文件选择区域"""
        file_frame = tk.LabelFrame(
            parent,
            text="选择HTML文件",
            font=('微软雅黑', 10, 'bold'),
            bg='white',
            fg='#333333',
            padx=15,
            pady=10
        )
        file_frame.pack(fill='x', pady=(0, 10))
        
        # 文件路径输入框
        path_frame = tk.Frame(file_frame, bg='white')
        path_frame.pack(fill='x')
        
        self.path_entry = tk.Entry(
            path_frame,
            textvariable=self.file_path_var,
            font=('微软雅黑', 10),
            relief='solid',
            bd=1,
            state='readonly'
        )
        self.path_entry.pack(side='left', fill='x', expand=True, padx=(0, 10))
        
        browse_btn = tk.Button(
            path_frame,
            text="浏览",
            font=('微软雅黑', 9),
            bg='#f0f0f0',
            fg='#333333',
            relief='solid',
            bd=1,
            cursor='hand2',
            command=self.browse_html_file,
            padx=15
        )
        browse_btn.pack(side='right')
    
    def create_settings_section(self, parent):
        """创建设置区域"""
        settings_frame = tk.LabelFrame(
            parent,
            text="转换设置",
            font=('微软雅黑', 10, 'bold'),
            bg='white',
            fg='#333333',
            padx=15,
            pady=10
        )
        settings_frame.pack(fill='x', pady=(0, 10))
        
        # 第一行：质量和格式
        row1_frame = tk.Frame(settings_frame, bg='white')
        row1_frame.pack(fill='x', pady=(0, 10))
        
        # 质量设置
        quality_label = tk.Label(
            row1_frame,
            text="质量:",
            font=('微软雅黑', 9),
            bg='white',
            fg='#666666'
        )
        quality_label.pack(side='left')
        
        quality_combo = ttk.Combobox(
            row1_frame,
            textvariable=self.quality_var,
            values=list(DEFAULT_CONFIG['quality_settings'].keys()),
            state='readonly',
            width=12,
            font=('微软雅黑', 9)
        )
        quality_combo.pack(side='left', padx=(5, 20))
        
        # 格式设置
        format_label = tk.Label(
            row1_frame,
            text="格式:",
            font=('微软雅黑', 9),
            bg='white',
            fg='#666666'
        )
        format_label.pack(side='left')
        
        format_combo = ttk.Combobox(
            row1_frame,
            textvariable=self.format_var,
            values=list(DEFAULT_CONFIG['format_map'].keys()),
            state='readonly',
            width=8,
            font=('微软雅黑', 9)
        )
        format_combo.pack(side='left', padx=(5, 0))
        
        # 自动裁剪选项
        self.auto_crop_check = tk.Checkbutton(
            settings_frame,
            text="自动裁剪白边",
            variable=self.auto_crop_var,
            font=('微软雅黑', 9),
            bg='white',
            fg='#666666',
            activebackground='white',
            activeforeground='#333333'        )
        self.auto_crop_check.pack(anchor='w', pady=5)
    
    def create_progress_section(self, parent):
        """创建进度条区域（在终端输出上方）"""
        self.progress_frame = tk.Frame(parent, bg='white')
        self.progress_frame.pack(fill='x', pady=(2, 5))
        
        # 进度条标签
        self.progress_label = tk.Label(
            self.progress_frame,
            text="转换进度:",
            font=('微软雅黑', 9),
            bg='white',
            fg='#666666'
        )
        self.progress_label.pack(anchor='w', pady=(0, 2))
          # 进度条
        self.progress_bar = ttk.Progressbar(
            self.progress_frame,
            variable=self.progress_var,
            mode='determinate',
            style='Custom.Horizontal.TProgressbar'
        )
        self.progress_bar.pack(fill='x', pady=(0, 2))
        
        # 进度百分比标签
        self.progress_percent_label = tk.Label(
            self.progress_frame,
            text="0%",
            font=('微软雅黑', 8),
            bg='white',
            fg='#666666'
        )
        self.progress_percent_label.pack(anchor='e')
        
        # 初始时显示进度条（这样用户能看到它的位置）
        # self.hide_progress()  # 暂时注释掉隐藏
        
    def create_terminal_section(self, parent):
        """创建终端输出区域（紧凑布局）"""
        self.terminal_frame = tk.Frame(parent, bg='white')
        self.terminal_frame.pack(fill='x', pady=(2, 2))
        
        # 终端输出区域标签
        self.output_label = tk.Label(
            self.terminal_frame,
            text="终端输出:",
            font=('微软雅黑', 9),
            bg='white',
            fg='#666666'
        )
        self.output_label.pack(anchor='w', pady=(0, 2))
        
        # 创建文本框框架
        text_frame = tk.Frame(self.terminal_frame, bg='white')
        text_frame.pack(fill='x')
        
        # 终端输出文本框（灰色背景）
        self.output_text = tk.Text(
            text_frame,
            height=8,
            width=70,
            font=('Consolas', 9),
            bg='#f5f5f5',
            fg='#333333',
            insertbackground='#333333',
            selectbackground='#d1ecf1',
            wrap=tk.WORD,
            state=tk.DISABLED
        )
        
        # 滚动条
        scrollbar = tk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.output_text.yview)
        self.output_text.configure(yscrollcommand=scrollbar.set)
        
        # 打包文本框和滚动条
        self.output_text.pack(side=tk.LEFT, fill='both', expand=True)
        scrollbar.pack(side=tk.RIGHT, fill='y')
          # 清空按钮
        clear_btn = tk.Button(
            self.terminal_frame,
            text="清空输出",
            font=('微软雅黑', 8),
            bg='#f0f0f0',
            fg='#333333',
            relief='flat',
            cursor='hand2',
            command=self.clear_output
        )
        clear_btn.pack(anchor='e', pady=(2, 0))
        
    def show_progress(self):
        """显示进度条"""
        self.progress_frame.pack(fill='x', pady=(2, 5))
        
    def hide_progress(self):
        """隐藏进度条"""
        self.progress_frame.pack_forget()
        
    def update_progress(self, value, text=""):
        """更新进度条"""
        self.progress_var.set(value)
        self.progress_percent_label.config(text=f"{int(value)}%")
        if text:
            # 将详细信息显示在进度条标签中
            self.progress_label.config(text=f"转换进度: {text}")
        self.root.update_idletasks()
    
    def clear_output(self):
        """清空输出文本"""
        self.output_text.config(state=tk.NORMAL)
        self.output_text.delete(1.0, tk.END)
        self.output_text.config(state=tk.DISABLED)
        
    def add_output(self, text):
        """添加输出文本"""
        self.output_text.config(state=tk.NORMAL)
        
        # 添加时间戳
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        
        # 插入文本
        self.output_text.insert(tk.END, f"[{timestamp}] {text}\n")
        
        # 自动滚动到底部
        self.output_text.see(tk.END)
        
        self.output_text.config(state=tk.DISABLED)
        self.root.update_idletasks()

    # === 功能方法 ===
    def browse_html_file(self):
        """浏览HTML文件"""
        file_path = filedialog.askopenfilename(
            title="选择HTML文件",
            filetypes=[("HTML files", "*.html *.htm"), ("All files", "*.*")]
        )
        
        if file_path:
            self.file_path_var.set(file_path)
            self.current_html_file = file_path
            self.status_var.set(f"已选择: {os.path.basename(file_path)}")
            self.add_output(f"选择文件: {file_path}")

    def save_as_image(self):
        """保存为图片"""
        if self.is_converting:
            return
            
        if not self.current_html_file:
            messagebox.showwarning("提示", "请先选择HTML文件")
            return
        
        if not os.path.exists(self.current_html_file):
            messagebox.showerror("错误", "文件不存在")
            return
        
        if not self.screenshot_handler:
            messagebox.showerror("错误", "截图处理器未初始化")
            return
        
        # 获取保存路径
        file_extension = DEFAULT_CONFIG['format_map'].get(self.format_var.get(), "png")
        save_path = filedialog.asksaveasfilename(
            title="保存图片",
            defaultextension=f".{file_extension}",
            filetypes=[(f"{self.format_var.get()} files", f"*.{file_extension}"), ("All files", "*.*")]
        )
        
        if not save_path:
            return
        
        self.add_output(f"开始转换: {os.path.basename(self.current_html_file)} -> {os.path.basename(save_path)}")
        
        # 开始转换
        self.start_conversion(save_path)
    
    def start_conversion(self, save_path):
        """开始转换（在后台线程中执行）"""
        self.is_converting = True
        self.convert_btn.config(text="转换中...", state='disabled', bg='#cccccc')
        self.show_progress()
        self.update_progress(0, "正在初始化...")
        
        # 在后台线程中执行转换
        def conversion_thread():
            try:
                # 调用截图处理器，传入进度回调
                self.screenshot_handler.process_screenshot(
                    html_path=self.current_html_file,
                    save_path=save_path,
                    quality=self.quality_var.get(),
                    format_type=self.format_var.get(),
                    screenshot_type="full_page",
                    enable_vector=True,
                    anti_aliasing=True,
                    auto_crop=self.auto_crop_var.get(),
                    success_callback=self._on_success,
                    error_callback=self._on_error,
                    progress_callback=self._on_progress
                )
            except Exception as e:
                self.root.after(0, lambda: self._on_error(str(e)))
        
        thread = threading.Thread(target=conversion_thread, daemon=True)
        thread.start()
    
    def _on_progress(self, progress, message=""):
        """进度回调（在主线程中调用）"""
        self.root.after(0, lambda: self.update_progress(progress, message))
        if message:
            self.root.after(0, lambda: self.add_output(message))
    
    def _on_success(self, save_path):
        """成功回调"""
        self.root.after(0, lambda: self._finish_conversion(save_path, True))
        self.root.after(0, lambda: self.add_output(f"✓ 转换成功: {save_path}"))
    
    def _on_error(self, error_message):
        """错误回调"""
        self.root.after(0, lambda: self._finish_conversion(error_message, False))
        self.root.after(0, lambda: self.add_output(f"✗ 转换失败: {error_message}"))
    
    def _finish_conversion(self, result, success):
        """完成转换"""
        self.is_converting = False
        self.convert_btn.config(text="开始转换", state='normal', bg='#0078d4')
        
        if success:
            self.update_progress(100, "转换完成")
            try:
                file_size = os.path.getsize(result) / (1024 * 1024)
                self._show_success_dialog(result, file_size)
            except Exception:
                self._show_success_dialog(result, None)
        else:
            self.update_progress(0, "转换失败")
            self._show_error_dialog(result)
    
    def _show_success_dialog(self, save_path, file_size):
        """显示成功对话框"""
        dialog = tk.Toplevel(self.root)
        dialog.title("转换成功")
        dialog.geometry("400x250")
        dialog.configure(bg='white')
        dialog.resizable(False, False)
        dialog.transient(self.root)
        dialog.grab_set()
        
        # 居中对话框
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() - 400) // 2
        y = (dialog.winfo_screenheight() - 250) // 2
        dialog.geometry(f"400x250+{x}+{y}")
        
        # 成功图标和文字
        success_frame = tk.Frame(dialog, bg='white')
        success_frame.pack(pady=20)
        
        success_label = tk.Label(
            success_frame,
            text="✅ 转换完成",
            font=('微软雅黑', 14, 'bold'),
            bg='white',
            fg='#28a745'
        )
        success_label.pack()
        
        # 文件信息
        info_frame = tk.Frame(dialog, bg='white')
        info_frame.pack(pady=10, padx=20, fill='x')
        
        file_label = tk.Label(
            info_frame,
            text=f"文件: {os.path.basename(save_path)}",
            font=('微软雅黑', 10),
            bg='white',
            fg='#333333'
        )
        file_label.pack(anchor='w')
        
        if file_size is not None:
            size_label = tk.Label(
                info_frame,
                text=f"大小: {file_size:.2f} MB",
                font=('微软雅黑', 10),
                bg='white',
                fg='#666666'
            )
            size_label.pack(anchor='w')
        
        # 按钮
        btn_frame = tk.Frame(dialog, bg='white')
        btn_frame.pack(pady=20)
        
        def open_folder():
            try:
                folder_path = os.path.dirname(save_path)
                os.startfile(folder_path)
            except Exception:
                pass
            dialog.destroy()
        
        open_btn = tk.Button(
            btn_frame,
            text="打开文件夹",
            font=('微软雅黑', 10),
            bg='#0078d4',
            fg='white',
            relief='flat',
            cursor='hand2',
            command=open_folder,
            padx=20
        )
        open_btn.pack(side='left', padx=(0, 10))
        
        def close_dialog():
            dialog.destroy()
        
        close_btn = tk.Button(
            btn_frame,
            text="关闭",
            font=('微软雅黑', 10),
            bg='#f0f0f0',
            fg='#333333',
            relief='flat',
            cursor='hand2',
            command=close_dialog,
            padx=20
        )
        close_btn.pack(side='left')
    
    def _show_error_dialog(self, error_message):
        """显示错误对话框"""
        dialog = tk.Toplevel(self.root)
        dialog.title("转换失败")
        dialog.geometry("500x400")
        dialog.configure(bg='white')
        dialog.resizable(True, True)
        dialog.transient(self.root)
        dialog.grab_set()
        
        # 居中对话框
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() - 500) // 2
        y = (dialog.winfo_screenheight() - 400) // 2
        dialog.geometry(f"500x400+{x}+{y}")
        
        # 错误图标和文字
        error_frame = tk.Frame(dialog, bg='white')
        error_frame.pack(pady=20)
        
        error_label = tk.Label(
            error_frame,
            text="❌ 转换失败",
            font=('微软雅黑', 14, 'bold'),
            bg='white',
            fg='#dc3545'
        )
        error_label.pack()
        
        # 错误信息
        text_frame = tk.Frame(dialog, bg='white')
        text_frame.pack(pady=10, padx=20, fill='both', expand=True)
        
        error_text = tk.Text(
            text_frame,
            font=('Consolas', 9),
            bg='#f8f9fa',
            fg='#333333',
            wrap=tk.WORD,
            yscrollcommand=None
        )
        
        scrollbar = tk.Scrollbar(text_frame, orient=tk.VERTICAL)
        error_text.config(
            yscrollcommand=scrollbar.set,
        )
        scrollbar.config(command=error_text.yview)
        
        error_text.pack(side=tk.LEFT, fill='both', expand=True)
        scrollbar.pack(side=tk.RIGHT, fill='y')
        
        error_text.insert('1.0', error_message)
        error_text.config(state='disabled')
        
        # 建议解决方案
        suggestion_frame = tk.Frame(dialog, bg='white')
        suggestion_frame.pack(pady=10, padx=20, fill='x')
        
        suggestion_label = tk.Label(
            suggestion_frame,
            text="💡 建议:",
            font=('微软雅黑', 10, 'bold'),
            bg='white',
            fg='#6c757d'
        )
        suggestion_label.pack(anchor='w')
        
        suggestions = [
            "1. 检查HTML文件是否可正常打开",
            "2. 确保文件路径中没有特殊字符",
            "3. 检查磁盘空间是否充足",
            "4. 尝试选择不同的输出格式"
        ]
        
        for suggestion in suggestions:
            sug_label = tk.Label(
                suggestion_frame,
                text=suggestion,
                font=('微软雅黑', 9),
                bg='white',
                fg='#6c757d'
            )
            sug_label.pack(anchor='w')
        
        # 关闭按钮
        btn_frame = tk.Frame(dialog, bg='white')
        btn_frame.pack(pady=20)
        
        def close_dialog():
            dialog.destroy()
        
        close_btn = tk.Button(
            btn_frame,
            text="关闭",
            font=('微软雅黑', 10),
            bg='#f0f0f0',
            fg='#333333',
            relief='flat',
            cursor='hand2',
            command=close_dialog,
            padx=20
        )
        close_btn.pack()
    
    def run(self):
        """运行GUI"""
        self.root.mainloop()


# 使用简约界面作为默认界面
HTMLToImageGUI = MinimalGUI
