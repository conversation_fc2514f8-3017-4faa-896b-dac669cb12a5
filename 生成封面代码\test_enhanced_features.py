#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强功能
验证扩展的颜色库、图标库和颜色数据库功能
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from config.themes import get_all_theme_names, get_themes_by_category, get_alternative_theme
from config.icons import get_all_icon_names, get_icons_by_category, get_smart_icons
from src.color_database import ColorDatabase
from src.api_client import DeepSeekClient


def test_expanded_themes():
    """测试扩展的主题色库"""
    print("=" * 60)
    print("测试扩展主题色库")
    print("=" * 60)
    
    all_themes = get_all_theme_names()
    print(f"总共有 {len(all_themes)} 种主题色:")
    
    # 按类别显示
    categories = ['蓝色系', '绿色系', '红色系', '紫色系', '橙色系', '金色系', '灰色系', '青色系']
    
    for category in categories:
        themes = get_themes_by_category(category)
        print(f"\n{category}: {themes}")
    
    # 测试替代主题
    print(f"\n科技蓝的替代主题: {get_alternative_theme('科技蓝')}")
    print(f"商务金的替代主题: {get_alternative_theme('商务金')}")


def test_expanded_icons():
    """测试扩展的图标库"""
    print("\n" + "=" * 60)
    print("测试扩展图标库")
    print("=" * 60)
    
    all_icons = get_all_icon_names()
    print(f"总共有 {len(all_icons)} 个图标:")
    
    # 按类别显示
    categories = ['数据分析', '科技', '商务', '财务', '法律', '教育', '文档', '建筑', '展示', '国际', '创新', '科研']
    
    for category in categories:
        icons = get_icons_by_category(category)
        if icons:
            print(f"\n{category}: {icons}")
    
    # 测试智能图标选择
    print(f"\n为'商学院'智能选择的图标:")
    smart_icons = get_smart_icons('商学院', 4)
    for icon in smart_icons:
        print(f"  - {icon['name']}: {icon['description']}")
    
    print(f"\n为'计算机学院'智能选择的图标:")
    smart_icons = get_smart_icons('计算机学院', 4)
    for icon in smart_icons:
        print(f"  - {icon['name']}: {icon['description']}")


def test_color_database():
    """测试颜色数据库功能"""
    print("\n" + "=" * 60)
    print("测试颜色数据库")
    print("=" * 60)
    
    db = ColorDatabase()
    
    # 测试记录和推荐
    test_cases = [
        ("电子科技大学", "经济与管理学院", "科技蓝"),
        ("电子科技大学", "计算机科学与工程学院", "科技蓝"),  # 应该被替换
        ("电子科技大学", "信息与软件工程学院", "深海蓝"),    # 应该被替换
        ("清华大学", "经济管理学院", "商务金"),
        ("清华大学", "计算机科学与技术系", "科技蓝"),
    ]
    
    for university, college, suggested_color in test_cases:
        print(f"\n测试: {university} - {college}")
        print(f"AI建议颜色: {suggested_color}")
        
        recommended = db.get_recommended_color(university, college, suggested_color)
        print(f"推荐颜色: {recommended}")
        
        # 记录使用
        db.record_color_usage(university, college, recommended)
    
    # 显示统计信息
    stats = db.get_statistics()
    print(f"\n数据库统计:")
    print(f"  总大学数: {stats['total_universities']}")
    print(f"  总学院数: {stats['total_colleges']}")
    print(f"  最常用颜色: {stats['most_used_color']}")
    print(f"  颜色使用统计: {stats['color_usage']}")


def test_api_integration():
    """测试API集成功能"""
    print("\n" + "=" * 60)
    print("测试API集成（模拟）")
    print("=" * 60)
    
    client = DeepSeekClient()
    
    # 测试学院分析
    test_colleges = [
        "电子科技大学经济与管理学院",
        "电子科技大学计算机科学与工程学院",
        "清华大学经济管理学院",
        "北京大学光华管理学院",
        "复旦大学管理学院"
    ]
    
    for college_name in test_colleges:
        print(f"\n分析: {college_name}")
        try:
            # 使用备用分析（避免API调用）
            result = client._fallback_analysis(college_name)
            print(f"  大学: {result['university']}")
            print(f"  学院: {result['college']}")
            print(f"  推荐主题: {result['theme_color']}")
        except Exception as e:
            print(f"  分析失败: {e}")


def test_complete_workflow():
    """测试完整工作流程"""
    print("\n" + "=" * 60)
    print("测试完整工作流程")
    print("=" * 60)
    
    from src.html_processor import HTMLProcessor
    
    processor = HTMLProcessor()
    
    # 测试HTML生成
    test_case = {
        'university': '电子科技大学',
        'college': '经济与管理学院',
        'theme_name': '科技蓝'
    }
    
    print(f"生成HTML预览: {test_case['university']} - {test_case['college']}")
    
    try:
        preview = processor.preview_html(
            test_case['university'], 
            test_case['college'], 
            test_case['theme_name'], 
            10
        )
        
        if preview:
            print("HTML预览生成成功（前10行）")
            print("包含智能选择的图标和优化的颜色")
        else:
            print("HTML预览生成失败")
            
    except Exception as e:
        print(f"HTML生成测试失败: {e}")


def main():
    """主测试函数"""
    print("🚀 开始测试增强功能")
    
    try:
        test_expanded_themes()
        test_expanded_icons()
        test_color_database()
        test_api_integration()
        test_complete_workflow()
        
        print("\n" + "=" * 60)
        print("✅ 所有测试完成！")
        print("=" * 60)
        
        print("\n增强功能总结:")
        print("✓ 主题色从7种扩展到20种，按色系分类")
        print("✓ 图标从10个扩展到25个，按类别分类")
        print("✓ 实现智能图标选择，根据学院类型匹配")
        print("✓ 实现颜色数据库，避免同校重复颜色")
        print("✓ 支持同色系替代颜色选择")
        print("✓ 完整集成到现有工作流程")
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
