"""
HTML转图片转换器
基于现有的html转图片样例代码进行适配
"""

import os
import sys
import time
import threading
import tempfile
from typing import Optional, Callable

# 添加html转图片样例代码路径
html_converter_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "html转图片样例代码")
if html_converter_path not in sys.path:
    sys.path.append(html_converter_path)

try:
    from playwright.sync_api import sync_playwright
except ImportError:
    print("警告: playwright未安装，HTML转图片功能将不可用")
    sync_playwright = None

class HTMLToImageConverter:
    def __init__(self):
        self.viewport = {"width": 750, "height": 1000}
        self.quality_scale = 2
        self.timeout = 60000  # 60秒超时
    
    def convert_html_to_png(self, html_content: str, output_path: str, 
                           progress_callback: Optional[Callable] = None) -> bool:
        """
        将HTML内容转换为PNG图片
        
        Args:
            html_content: HTML内容字符串
            output_path: 输出PNG文件路径
            progress_callback: 进度回调函数
        
        Returns:
            bool: 转换是否成功
        """
        if not sync_playwright:
            print("错误: playwright未安装，无法进行HTML转图片")
            return False
        
        try:
            # 更新进度
            if progress_callback:
                progress_callback(10, "正在准备HTML文件...")
            
            # 创建临时HTML文件
            temp_html = tempfile.NamedTemporaryFile(mode='w', suffix='.html', 
                                                   encoding='utf-8', delete=False)
            temp_html.write(html_content)
            temp_html.close()
            
            if progress_callback:
                progress_callback(20, "正在启动浏览器...")
            
            # 使用playwright进行截图
            with sync_playwright() as p:
                # 启动浏览器
                browser = p.chromium.launch(
                    headless=True,
                    args=[
                        '--no-sandbox',
                        '--disable-setuid-sandbox',
                        '--disable-dev-shm-usage',
                        '--disable-web-security',
                        '--disable-features=VizDisplayCompositor',
                        f'--force-device-scale-factor={self.quality_scale}'
                    ]
                )
                
                if progress_callback:
                    progress_callback(40, "正在加载页面...")
                
                try:
                    # 创建页面
                    context = browser.new_context(
                        viewport=self.viewport,
                        device_scale_factor=self.quality_scale
                    )
                    page = context.new_page()
                    
                    # 设置超时
                    page.set_default_timeout(self.timeout)
                    
                    # 加载HTML文件
                    file_url = f"file:///{os.path.abspath(temp_html.name).replace(os.sep, '/')}"
                    page.goto(file_url, wait_until='domcontentloaded', timeout=self.timeout)
                    
                    if progress_callback:
                        progress_callback(60, "正在等待页面渲染...")
                    
                    # 等待内容加载完成
                    time.sleep(3)
                    
                    # 优化页面渲染
                    self._optimize_page_rendering(page)
                    
                    if progress_callback:
                        progress_callback(80, "正在生成PNG图片...")
                    
                    # 截图
                    page.screenshot(
                        path=output_path,
                        full_page=True,
                        type='png'
                    )
                    
                    if progress_callback:
                        progress_callback(100, "转换完成")
                    
                    print(f"✓ HTML转PNG成功: {output_path}")
                    return True
                    
                finally:
                    browser.close()
                    # 清理临时文件
                    try:
                        os.unlink(temp_html.name)
                    except:
                        pass
        
        except Exception as e:
            print(f"HTML转PNG失败: {e}")
            if progress_callback:
                progress_callback(0, f"转换失败: {str(e)}")
            
            # 清理临时文件
            try:
                os.unlink(temp_html.name)
            except:
                pass
            
            return False
    
    def convert_html_to_png_async(self, html_content: str, output_path: str,
                                 success_callback: Optional[Callable] = None,
                                 error_callback: Optional[Callable] = None,
                                 progress_callback: Optional[Callable] = None) -> threading.Thread:
        """
        异步将HTML转换为PNG
        """
        def conversion_thread():
            try:
                success = self.convert_html_to_png(html_content, output_path, progress_callback)
                if success and success_callback:
                    success_callback(output_path)
                elif not success and error_callback:
                    error_callback("转换失败")
            except Exception as e:
                if error_callback:
                    error_callback(str(e))
        
        thread = threading.Thread(target=conversion_thread, daemon=True)
        thread.start()
        return thread
    
    def _optimize_page_rendering(self, page):
        """优化页面渲染质量"""
        try:
            page.evaluate("""
                (() => {
                    // 优化字体渲染
                    document.body.style.webkitFontSmoothing = 'antialiased';
                    document.body.style.mozOsxFontSmoothing = 'grayscale';
                    document.body.style.textRendering = 'optimizeLegibility';
                    
                    // 优化图像渲染
                    const images = document.querySelectorAll('img');
                    images.forEach(img => {
                        img.style.imageRendering = 'high-quality';
                        img.style.imageRendering = '-webkit-optimize-contrast';
                    });
                    
                    // 优化SVG渲染
                    const svgs = document.querySelectorAll('svg');
                    svgs.forEach(svg => {
                        svg.style.shapeRendering = 'geometricPrecision';
                    });
                    
                    // 强制重绘
                    document.body.style.transform = 'translateZ(0)';
                })();
            """)
            
            # 等待渲染完成
            time.sleep(2)
            
            # 滚动到顶部
            page.evaluate("window.scrollTo(0, 0)")
            time.sleep(1)
            
        except Exception as e:
            print(f"页面渲染优化失败: {e}")

# 创建全局实例
html_to_image_converter = HTMLToImageConverter()
