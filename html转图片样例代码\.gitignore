# 字节码文件
__pycache__/
*.py[cod]
*$py.class

# 分布和打包
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# 单元测试和覆盖率
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# pipenv
Pipfile.lock

# 环境变量
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统
.DS_Store
Thumbs.db
Desktop.ini

# 项目特定
*.html
*.png
*.jpg
*.jpeg
*.pdf
*.svg
*.webp
temp/
output/
screenshots/

# 日志文件
*.log
logs/

# 临时文件
*.tmp
*.temp
