#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基本配置文件
"""

import os

# 项目根目录
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

# 应用配置
APP_CONFIG = {
    'app_name': '千帆封面图生成工具',
    'version': '1.0.0',
    'author': 'AI助手',
    
    # 目录配置
    'project_root': PROJECT_ROOT,
    'output_dir': os.path.join(PROJECT_ROOT, 'output'),
    'templates_dir': os.path.join(PROJECT_ROOT, 'templates'),
    'assets_dir': os.path.join(PROJECT_ROOT, 'assets'),
    
    # DeepSeek API配置
    'deepseek_api': {
        'base_url': 'https://api.deepseek.com',
        'api_key': '***********************************',
        'model': 'deepseek-chat',
        'timeout': 30,
        'max_retries': 3
    },
    
    # HTML转图片配置
    'screenshot': {
        'viewport': {'width': 1920, 'height': 1080},
        'scale': 2.0,  # 高清缩放
        'format': 'PNG',
        'quality': 95,
        'timeout': 60000,  # 60秒超时
        'auto_crop': True,  # 自动裁剪白边
        'anti_aliasing': True  # 抗锯齿
    },
    
    # 文件命名配置
    'naming': {
        'html_suffix': '封面设计.html',
        'image_suffix': '封面图.png',
        'folder_format': '{university}_{college}',
        'file_format': '{university}_{college}_{suffix}'
    }
}

# DeepSeek API系统提示词
DEEPSEEK_SYSTEM_PROMPT = """你是一个专业的教育机构品牌设计助手。你的任务是：

1. 从用户提供的学院名称中准确提取大学名称和学院名称
2. 根据学院特点推荐最适合的主题色

请严格按照以下JSON格式回复，不要添加任何其他内容：
{
    "university": "大学名称",
    "college": "学院名称", 
    "theme_color": "推荐的主题色名称"
}

可选的主题色包括：
- 科技蓝：适合理工科、计算机、电子信息类学院
- 商务金：适合商学院、经济管理类学院
- 学术绿：适合文学院、教育学院、人文社科类
- 医学红：适合医学院、生命科学类学院
- 法政紫：适合法学院、政治学院、公共管理类
- 艺术橙：适合艺术学院、设计学院、传媒类
- 工程灰：适合工程学院、建筑学院、土木类

请根据学院的学科特点选择最合适的主题色。"""
