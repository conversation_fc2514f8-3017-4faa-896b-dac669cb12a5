#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基本配置文件
"""

import os

# 项目根目录
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

# 应用配置
APP_CONFIG = {
    'app_name': '千帆封面图生成工具',
    'version': '1.0.0',
    'author': 'AI助手',
    
    # 目录配置
    'project_root': PROJECT_ROOT,
    'output_dir': os.path.join(PROJECT_ROOT, 'output'),
    'templates_dir': os.path.join(PROJECT_ROOT, 'templates'),
    'assets_dir': os.path.join(PROJECT_ROOT, 'assets'),
    
    # DeepSeek API配置
    'deepseek_api': {
        'base_url': 'https://api.deepseek.com',
        'api_key': '***********************************',
        'model': 'deepseek-chat',
        'timeout': 30,
        'max_retries': 3
    },
    
    # HTML转图片配置
    'screenshot': {
        'viewport': {'width': 1920, 'height': 1080},
        'scale': 2.0,  # 高清缩放
        'format': 'PNG',
        'quality': 95,
        'timeout': 60000,  # 60秒超时
        'auto_crop': True,  # 自动裁剪白边
        'anti_aliasing': True  # 抗锯齿
    },
    
    # 文件命名配置
    'naming': {
        'html_suffix': '封面设计.html',
        'image_suffix': '封面图.png',
        'folder_format': '{university}_{college}',
        'file_format': '{university}_{college}_{suffix}'
    }
}

# DeepSeek API系统提示词
DEEPSEEK_SYSTEM_PROMPT = """你是一个专业的教育机构品牌设计助手。你的任务是：

1. 从用户提供的学院名称中准确提取大学名称和学院名称
2. 根据学院特点推荐最适合的主题色

请严格按照以下JSON格式回复，不要添加任何其他内容：
{
    "university": "大学名称",
    "college": "学院名称", 
    "theme_color": "推荐的主题色名称"
}

可选的主题色包括：

蓝色系：
- 科技蓝：适合理工科、计算机、电子信息类学院
- 深海蓝：适合海洋科学、水利工程类学院
- 天空蓝：适合航空航天、气象类学院

绿色系：
- 学术绿：适合文学院、教育学院、人文社科类
- 森林绿：适合林学院、环境科学类学院
- 翡翠绿：适合生物科学、农学类学院

红色系：
- 医学红：适合医学院、生命科学类学院
- 玫瑰红：适合护理学院、心理学类学院
- 酒红色：适合历史学院、考古类学院

紫色系：
- 法政紫：适合法学院、政治学院、公共管理类
- 神秘紫：适合哲学院、宗教学类学院
- 薰衣草紫：适合外国语学院、文化传播类学院

橙色系：
- 艺术橙：适合艺术学院、设计学院、传媒类
- 活力橙：适合体育学院、运动科学类学院
- 琥珀橙：适合地质学院、材料科学类学院

金色系：
- 商务金：适合商学院、经济管理类学院
- 皇家金：适合金融学院、财经类学院
- 香槟金：适合会计学院、审计类学院

灰色系：
- 工程灰：适合工程学院、建筑学院、土木类
- 钢铁灰：适合机械学院、冶金类学院
- 石墨灰：适合化学学院、材料工程类学院

青色系：
- 青瓷色：适合陶瓷学院、工艺美术类学院
- 湖水青：适合水产学院、海洋工程类学院

请根据学院的学科特点选择最合适的主题色。"""
