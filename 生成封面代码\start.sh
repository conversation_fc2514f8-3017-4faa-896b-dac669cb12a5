#!/bin/bash

# 千帆封面图生成工具 - Linux/Mac启动脚本

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印标题
echo -e "${BLUE}"
echo "████████████████████████████████████████████████████████"
echo "█                                                      █"
echo "█            千帆封面图生成工具 v1.0.0                 █"
echo "█                                                      █"
echo "████████████████████████████████████████████████████████"
echo -e "${NC}"

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 切换到脚本目录
cd "$SCRIPT_DIR"

echo -e "📍 当前目录: ${BLUE}$PWD${NC}"
echo

# 检查Python环境
echo -e "🔍 检查Python环境..."
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo -e "${RED}❌ 未找到Python${NC}"
        echo
        echo "请安装Python 3.8或更高版本"
        echo "Ubuntu/Debian: sudo apt install python3 python3-pip"
        echo "CentOS/RHEL: sudo yum install python3 python3-pip"
        echo "macOS: brew install python3"
        echo
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

# 检查Python版本
PYTHON_VERSION=$($PYTHON_CMD --version 2>&1 | cut -d' ' -f2)
echo -e "${GREEN}✅ Python版本: $PYTHON_VERSION${NC}"

# 检查版本是否满足要求
PYTHON_MAJOR=$($PYTHON_CMD -c "import sys; print(sys.version_info.major)")
PYTHON_MINOR=$($PYTHON_CMD -c "import sys; print(sys.version_info.minor)")

if [ "$PYTHON_MAJOR" -lt 3 ] || ([ "$PYTHON_MAJOR" -eq 3 ] && [ "$PYTHON_MINOR" -lt 8 ]); then
    echo -e "${RED}❌ Python版本过低，需要3.8或更高版本${NC}"
    exit 1
fi

# 检查主程序文件
echo
echo -e "🔍 检查程序文件..."
if [ ! -f "main.py" ]; then
    echo -e "${RED}❌ 未找到 main.py 文件${NC}"
    echo
    echo "请确保在正确的目录中运行此脚本"
    echo "当前目录应包含以下文件:"
    echo "  - main.py"
    echo "  - requirements.txt"
    echo "  - config/ 文件夹"
    echo "  - src/ 文件夹"
    echo
    exit 1
fi
echo -e "${GREEN}✅ 找到主程序文件${NC}"

# 检查配置文件夹
if [ ! -d "config" ]; then
    echo -e "${RED}❌ 未找到 config 文件夹${NC}"
    exit 1
fi
if [ ! -d "src" ]; then
    echo -e "${RED}❌ 未找到 src 文件夹${NC}"
    exit 1
fi
echo -e "${GREEN}✅ 程序结构完整${NC}"

# 检查是否有start.py
if [ -f "start.py" ]; then
    echo
    echo -e "🚀 启动程序..."
    echo "================================================"
    echo
    
    # 使用Python启动脚本
    $PYTHON_CMD start.py
    EXIT_CODE=$?
else
    echo
    echo -e "🚀 直接启动主程序..."
    echo "================================================"
    echo
    
    # 直接启动main.py
    $PYTHON_CMD main.py
    EXIT_CODE=$?
fi

echo
echo "================================================"
if [ $EXIT_CODE -eq 0 ]; then
    echo -e "${GREEN}✅ 程序正常退出${NC}"
else
    echo -e "${YELLOW}⚠️  程序异常退出 (错误代码: $EXIT_CODE)${NC}"
fi

echo
echo "感谢使用千帆封面图生成工具！"

# 在某些终端中暂停
if [ -t 0 ]; then
    read -p "按回车键退出..."
fi

exit $EXIT_CODE
