#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HTML转图片模块
基于playwright实现HTML转高清PNG图片功能
"""

import os
import time
import threading
from typing import Optional, Callable
from playwright.sync_api import sync_playwright

from config.settings import APP_CONFIG


class ImageConverter:
    """HTML转图片转换器"""
    
    def __init__(self):
        self.config = APP_CONFIG['screenshot']
        
    def convert_html_to_image(self, html_path: str, output_path: str, 
                            progress_callback: Optional[Callable] = None,
                            success_callback: Optional[Callable] = None,
                            error_callback: Optional[Callable] = None) -> bool:
        """
        将HTML文件转换为高清PNG图片
        
        Args:
            html_path: HTML文件路径
            output_path: 输出图片路径
            progress_callback: 进度回调函数 (progress, message)
            success_callback: 成功回调函数 (output_path)
            error_callback: 错误回调函数 (error_message)
            
        Returns:
            是否转换成功
        """
        def conversion_thread():
            try:
                success = self._do_conversion(html_path, output_path, progress_callback)
                
                if success and success_callback:
                    success_callback(output_path)
                elif not success and error_callback:
                    error_callback("转换失败")
                    
                return success
                    
            except Exception as e:
                if error_callback:
                    error_callback(str(e))
                return False
        
        # 在新线程中执行转换
        thread = threading.Thread(target=conversion_thread, daemon=True)
        thread.start()
        return thread
    
    def _do_conversion(self, html_path: str, output_path: str, 
                      progress_callback: Optional[Callable] = None) -> bool:
        """
        执行实际的转换操作
        """
        try:
            # 进度更新函数
            def update_progress(progress: int, message: str = ""):
                if progress_callback:
                    progress_callback(progress, message)
            
            update_progress(5, "正在启动浏览器...")
            
            # 检查HTML文件是否存在
            if not os.path.exists(html_path):
                raise FileNotFoundError(f"HTML文件不存在: {html_path}")
            
            # 检查输出目录
            output_dir = os.path.dirname(output_path)
            if output_dir:
                os.makedirs(output_dir, exist_ok=True)
            
            with sync_playwright() as p:
                # 构建浏览器启动参数
                browser_args = [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-accelerated-2d-canvas',
                    '--no-first-run',
                    '--no-zygote',
                    '--disable-gpu',
                    f'--force-device-scale-factor={self.config["scale"]}'
                ]
                
                if self.config['anti_aliasing']:
                    browser_args.append('--enable-font-antialiasing')
                else:
                    browser_args.append('--disable-font-antialiasing')
                
                # 启动浏览器
                try:
                    browser = p.chromium.launch(
                        headless=True,
                        args=browser_args,
                        timeout=60000  # 1分钟启动超时
                    )
                    update_progress(15, "浏览器已启动，正在加载页面...")
                except Exception as e:
                    raise RuntimeError(f"无法启动浏览器: {str(e)}")
                
                try:
                    # 创建上下文
                    context = browser.new_context(
                        viewport={
                            'width': 750,  # 精确匹配封面宽度
                            'height': 1000  # 精确匹配封面高度
                        },
                        device_scale_factor=self.config['scale']
                    )
                    
                    page = context.new_page()
                    
                    # 设置页面超时
                    page.set_default_timeout(self.config['timeout'])
                    page.set_default_navigation_timeout(self.config['timeout'])
                    
                    update_progress(25, "正在加载HTML文件...")
                    
                    # 导航到HTML文件
                    file_url = f"file:///{os.path.abspath(html_path).replace(os.sep, '/')}"
                    print(f"加载文件: {file_url}")
                    
                    try:
                        page.goto(file_url, timeout=30000, wait_until='domcontentloaded')
                        print("DOM内容已加载")
                        
                        # 等待网络空闲
                        try:
                            page.wait_for_load_state('networkidle', timeout=15000)
                            print("网络空闲状态达到")
                        except Exception as e:
                            print(f"网络空闲等待超时，继续处理: {e}")
                        
                    except Exception as e:
                        raise RuntimeError(f"无法加载HTML文件: {str(e)}")
                    
                    update_progress(40, "页面加载完成，正在优化渲染...")
                    
                    # 等待内容加载
                    time.sleep(2)
                    
                    # 执行页面优化
                    try:
                        self._optimize_page_rendering(page)
                    except Exception as e:
                        print(f"页面优化警告: {e}")
                    
                    update_progress(60, "正在生成高清图片...")
                    
                    # 截图 - 精确截取视口大小
                    try:
                        # 等待封面容器加载
                        page.wait_for_selector('#cover-container', timeout=10000)

                        # 直接截取整个视口（已设置为750x1000）
                        screenshot_options = {
                            'path': output_path,
                            'type': 'png',
                            'full_page': False,  # 只截取视口
                            'quality': self.config['quality'] if self.config['format'] == 'JPEG' else None
                        }

                        print(f"开始截图，选项: {screenshot_options}")
                        page.screenshot(**screenshot_options)
                        
                    except Exception as e:
                        raise RuntimeError(f"截图失败: {str(e)}")
                    
                finally:
                    try:
                        browser.close()
                    except Exception:
                        pass
                
                update_progress(80, "正在处理图片...")
                
                # 检查文件是否成功生成
                if not os.path.exists(output_path):
                    raise RuntimeError("输出文件未能成功生成")
                
                # 图片后处理
                if self.config['auto_crop']:
                    try:
                        self._auto_crop_image(output_path)
                    except Exception as e:
                        print(f"图片裁剪警告: {e}")
                
                update_progress(100, "转换完成")
                print(f"✓ 转换成功: {output_path}")
                
                return True
                
        except FileNotFoundError as e:
            error_msg = f"文件错误: {str(e)}"
            print(error_msg)
            if progress_callback:
                progress_callback(0, error_msg)
            return False
        except RuntimeError as e:
            error_msg = f"运行时错误: {str(e)}"
            print(error_msg)
            if progress_callback:
                progress_callback(0, error_msg)
            return False
        except Exception as e:
            error_msg = f"未知错误: {str(e)}"
            print(f"转换过程中出错: {error_msg}")
            if progress_callback:
                progress_callback(0, error_msg)
            return False
    
    def _optimize_page_rendering(self, page):
        """优化页面渲染"""
        try:
            page.evaluate("""
                (() => {
                    // 优化字体渲染
                    document.body.style.webkitFontSmoothing = 'antialiased';
                    document.body.style.mozOsxFontSmoothing = 'grayscale';
                    document.body.style.textRendering = 'optimizeLegibility';
                    
                    // 优化图像渲染
                    const images = document.querySelectorAll('img');
                    images.forEach(img => {
                        img.style.imageRendering = 'high-quality';
                        img.style.imageRendering = '-webkit-optimize-contrast';
                    });
                    
                    // 优化SVG渲染
                    const svgs = document.querySelectorAll('svg');
                    svgs.forEach(svg => {
                        svg.style.shapeRendering = 'geometricPrecision';
                    });
                    
                    // 强制重绘
                    document.body.style.transform = 'translateZ(0)';
                    
                    // 等待所有图片加载完成
                    const allImages = Array.from(document.querySelectorAll('img'));
                    const imagePromises = allImages.map(img => {
                        if (img.complete) return Promise.resolve();
                        return new Promise((resolve) => {
                            img.addEventListener('load', resolve);
                            img.addEventListener('error', resolve);
                            setTimeout(resolve, 5000); // 5秒超时
                        });
                    });
                    
                    return Promise.all(imagePromises);
                })();
            """)
            
            # 额外等待确保渲染完成
            time.sleep(1)
            
            # 滚动到顶部
            page.evaluate("window.scrollTo(0, 0)")
            time.sleep(0.5)
            
        except Exception as e:
            print(f"页面渲染优化失败: {e}")
    
    def _auto_crop_image(self, image_path: str):
        """自动裁剪图片白边 - 强化版本，确保完全没有白边"""
        try:
            from PIL import Image
            import numpy as np

            # 打开图片
            with Image.open(image_path) as img:
                original_size = img.size

                # 转换为RGB模式
                if img.mode != 'RGB':
                    img = img.convert('RGB')

                # 多阶段白边检测，确保完全去除
                white_thresholds = [254, 250, 245, 240]  # 从最严格到较宽松

                cropped_img = img
                for threshold in white_thresholds:
                    # 找到非白色像素
                    non_white = np.any(np.array(cropped_img) < threshold, axis=2)

                    # 找到边界
                    rows = np.any(non_white, axis=1)
                    cols = np.any(non_white, axis=0)

                    if rows.any() and cols.any():
                        # 获取边界坐标
                        top, bottom = np.where(rows)[0][[0, -1]]
                        left, right = np.where(cols)[0][[0, -1]]

                        # 不添加边距，紧贴内容裁剪
                        # 裁剪图片
                        cropped_img = cropped_img.crop((left, top, right + 1, bottom + 1))
                        print(f"✓ 阈值{threshold}裁剪: {cropped_img.size}")
                    else:
                        print(f"⚠ 阈值{threshold}未检测到白边")
                        break

                # 额外的边缘清理 - 检查边缘是否还有接近白色的像素
                final_array = np.array(cropped_img)
                h, w = final_array.shape[:2]

                # 检查四个边缘
                edges_to_crop = {'top': 0, 'bottom': 0, 'left': 0, 'right': 0}

                # 检查顶部边缘
                for i in range(min(20, h)):
                    if np.mean(final_array[i, :, :]) > 240:
                        edges_to_crop['top'] = i + 1
                    else:
                        break

                # 检查底部边缘
                for i in range(min(20, h)):
                    if np.mean(final_array[h-1-i, :, :]) > 240:
                        edges_to_crop['bottom'] = i + 1
                    else:
                        break

                # 检查左边缘
                for i in range(min(20, w)):
                    if np.mean(final_array[:, i, :]) > 240:
                        edges_to_crop['left'] = i + 1
                    else:
                        break

                # 检查右边缘
                for i in range(min(20, w)):
                    if np.mean(final_array[:, w-1-i, :]) > 240:
                        edges_to_crop['right'] = i + 1
                    else:
                        break

                # 如果检测到边缘白边，进行最终裁剪
                if any(edges_to_crop.values()):
                    final_left = edges_to_crop['left']
                    final_top = edges_to_crop['top']
                    final_right = w - edges_to_crop['right']
                    final_bottom = h - edges_to_crop['bottom']

                    if final_right > final_left and final_bottom > final_top:
                        cropped_img = cropped_img.crop((final_left, final_top, final_right, final_bottom))
                        print(f"✓ 边缘清理裁剪: {cropped_img.size}")

                # 保存最终图片
                if cropped_img.size != original_size:
                    cropped_img.save(image_path, 'PNG', optimize=True, quality=95)
                    print(f"✓ 白边完全清除: {original_size} -> {cropped_img.size}")
                else:
                    print("⚠ 未检测到需要裁剪的白边")

        except ImportError:
            print("⚠ PIL/Pillow未安装，跳过图片裁剪")
        except Exception as e:
            print(f"图片裁剪失败: {e}")
    
    def test_conversion(self, html_content: str) -> bool:
        """
        测试转换功能
        
        Args:
            html_content: HTML内容
            
        Returns:
            是否测试成功
        """
        try:
            # 创建临时HTML文件
            import tempfile
            with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8') as f:
                f.write(html_content)
                temp_html = f.name
            
            # 创建临时输出文件
            temp_output = temp_html.replace('.html', '.png')
            
            try:
                # 执行转换
                success = self._do_conversion(temp_html, temp_output)
                
                if success and os.path.exists(temp_output):
                    print(f"✓ 转换测试成功，输出文件大小: {os.path.getsize(temp_output)} 字节")
                    return True
                else:
                    print("✗ 转换测试失败")
                    return False
                    
            finally:
                # 清理临时文件
                for temp_file in [temp_html, temp_output]:
                    if os.path.exists(temp_file):
                        try:
                            os.remove(temp_file)
                        except:
                            pass
                            
        except Exception as e:
            print(f"转换测试失败: {e}")
            return False
