# HTML转图片工具 - PyQt6现代化版

一个强大的HTML转图片/PDF工具，支持高质量转换和智能白边裁剪。

## ✨ 新特性

### 🎨 现代化PyQt6界面
- **美观的现代化界面设计**
- **实时终端输出显示** - 在界面下方的文本框中查看转换过程
- **进度条实时更新** - 直观显示转换进度
- **智能错误提示** - 详细的错误信息和解决方案

### 🚀 核心功能
- **多格式支持**: PNG、JPEG、WEBP、PDF
- **高质量转换**: 支持从标准到8K超清的多种清晰度
- **智能白边裁剪**: 自动检测并裁剪多余的白边
- **PDF矢量输出**: 文字可选择的高质量PDF输出
- **超时优化**: 解决HTML加载超时问题
- **一键打开文件夹**: 转换完成后快速定位输出文件

## 🔧 安装和使用

### 快速安装
1. 双击运行 `install_dependencies.bat` 自动安装所有依赖
2. 双击运行 `run.bat` 启动程序

### 手动安装
```bash
# 安装PyQt6界面框架
pip install PyQt6>=6.5.0

# 安装核心依赖
pip install playwright>=1.40.0 Pillow>=10.0.0 opencv-python>=4.8.0 numpy>=1.24.0 PyPDF2>=3.0.0 pymupdf>=1.23.0

# 安装playwright浏览器
python -m playwright install chromium
```

### 使用方法
1. 启动程序后，点击"浏览..."按钮选择HTML文件
2. 在设置区域选择输出格式和清晰度
3. 可选择是否启用自动裁剪白边和抗锯齿
4. 点击"开始转换"按钮
5. 在进度条下方的终端输出框中查看转换过程
6. 转换完成后可点击"打开输出文件夹"快速定位文件

## 🎯 界面特色

### 智能界面适配
- **优先使用PyQt6**: 现代化界面，更好的用户体验
- **Tkinter降级**: 如果PyQt6不可用，自动降级到tkinter界面
- **终端输出集成**: 在GUI中直接查看程序运行状态

### 转换设置
- **输出格式**: PNG、JPEG、WEBP、PDF
- **清晰度选项**: 标准清晰度、高清(HD)、全高清(FHD)、超高清(4K)、8K超清、矢量级别
- **智能选项**: 自动裁剪白边、抗锯齿渲染

## 🛠️ 技术特性

### 超时问题修复
- **HTML预处理**: 自动优化HTML文件，处理外部资源加载
- **多重加载策略**: 标准加载失败时自动尝试简化加载
- **增强的浏览器参数**: 优化浏览器启动参数，提高稳定性

### 输出质量优化
- **高DPI支持**: 支持高分辨率显示器
- **抗锯齿渲染**: 优化字体和图像渲染质量
- **PDF白边裁剪**: 自动裁剪PDF文件的多余白边

## 📁 项目结构

```
html转图片/
├── main.py                    # 主程序入口
├── run.bat                   # 启动脚本
├── install_dependencies.bat  # 依赖安装脚本
├── requirements.txt          # 依赖包列表
├── src/                      # 源代码目录
│   ├── __init__.py          # 模块初始化
│   ├── gui_pyqt6.py         # PyQt6现代化界面
│   ├── gui.py               # tkinter备用界面
│   ├── screenshot.py        # 截图处理模块
│   ├── config.py            # 配置文件
│   ├── image_processor.py   # 图像处理模块
│   ├── pdf_processor.py     # PDF处理模块
│   └── controller.py        # 控制器模块
└── README.md                # 说明文档
```

## 🐛 故障排除

### 常见问题
1. **PyQt6未安装**: 程序会自动降级到tkinter界面
2. **转换超时**: 程序已优化HTML加载策略，大幅减少超时问题
3. **权限错误**: 确保对输出目录有写入权限

### 日志查看
程序会在界面的终端输出框中显示详细的运行日志，包括：
- 依赖包检查结果
- HTML文件加载过程
- 转换进度信息
- 错误详情和解决建议

## 📄 版本历史

### v2.1 - PyQt6现代化版
- ✨ 全新PyQt6现代化界面设计
- 🖥️ 集成终端输出显示
- 🚀 修复HTML加载超时问题
- 🎨 优化用户体验和界面布局
- 📱 响应式界面设计

### v2.0.3 - PDF白边裁剪版
- 🔧 修复进度条显示问题
- 📄 强化PDF白边自动裁剪
- 📁 新增打开文件夹功能

## 👨‍💻 开发信息

- **作者**: AI助手
- **版本**: 2.1 - PyQt6现代化版
- **许可**: MIT License
- **Python版本要求**: 3.8+

---

💡 **提示**: 首次使用请运行 `install_dependencies.bat` 安装所有必需的依赖包。
