#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
学校颜色记录数据库模块
用于记录每个学校已使用的颜色，避免同一学校的不同学院使用相同颜色
"""

import os
import json
from typing import Dict, List, Optional
from config.themes import get_all_theme_names
from config.settings import APP_CONFIG


class ColorDatabase:
    """学校颜色记录数据库"""
    
    def __init__(self):
        self.config = APP_CONFIG
        self.db_file = os.path.join(self.config['project_root'], 'school_colors.json')
        self.data = self._load_database()
    
    def _load_database(self) -> Dict:
        """加载数据库文件"""
        try:
            if os.path.exists(self.db_file):
                with open(self.db_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                print(f"✓ 颜色数据库加载成功，包含 {len(data)} 所学校记录")
                return data
            else:
                print("✓ 创建新的颜色数据库")
                return {}
        except Exception as e:
            print(f"⚠ 颜色数据库加载失败: {e}，使用空数据库")
            return {}
    
    def _save_database(self) -> bool:
        """保存数据库文件"""
        try:
            with open(self.db_file, 'w', encoding='utf-8') as f:
                json.dump(self.data, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"⚠ 颜色数据库保存失败: {e}")
            return False
    
    def record_color_usage(self, university: str, college: str, theme_color: str) -> bool:
        """
        记录学校学院的颜色使用情况
        
        Args:
            university: 大学名称
            college: 学院名称
            theme_color: 使用的主题色
            
        Returns:
            是否记录成功
        """
        try:
            # 清理大学名称（去除多余字符）
            clean_university = self._clean_university_name(university)
            
            if clean_university not in self.data:
                self.data[clean_university] = {}
            
            # 记录学院和颜色
            self.data[clean_university][college] = {
                'theme_color': theme_color,
                'timestamp': self._get_timestamp()
            }
            
            # 保存到文件
            success = self._save_database()
            if success:
                print(f"✓ 记录颜色使用: {clean_university} - {college} - {theme_color}")
            
            return success
            
        except Exception as e:
            print(f"⚠ 记录颜色使用失败: {e}")
            return False
    
    def get_used_colors(self, university: str) -> List[str]:
        """
        获取某大学已使用的颜色列表
        
        Args:
            university: 大学名称
            
        Returns:
            已使用的颜色列表
        """
        clean_university = self._clean_university_name(university)
        
        if clean_university not in self.data:
            return []
        
        used_colors = []
        for college_info in self.data[clean_university].values():
            if isinstance(college_info, dict) and 'theme_color' in college_info:
                used_colors.append(college_info['theme_color'])
        
        return list(set(used_colors))  # 去重
    
    def get_recommended_color(self, university: str, college: str, ai_suggested_color: str = None) -> str:
        """
        获取推荐的颜色，同一学校使用同一色系的不同颜色变体

        Args:
            university: 大学名称
            college: 学院名称
            ai_suggested_color: AI建议的颜色（现在不使用，保留参数兼容性）

        Returns:
            推荐的颜色方案名称
        """
        try:
            from config.themes import get_color_scheme_for_university

            # 直接使用新的颜色生成系统
            # 这会自动确保同校同色系，但每个学院颜色略有不同
            color_scheme = get_color_scheme_for_university(university, college)

            # 生成一个唯一的颜色方案名称
            scheme_name = f"{university}_{college}_主题"

            # 将生成的颜色方案存储到THEME_COLORS中以供后续使用
            from config.themes import THEME_COLORS
            THEME_COLORS[scheme_name] = color_scheme

            print(f"✓ 为 {university}-{college} 生成颜色方案: {color_scheme['category']}")
            return scheme_name

        except Exception as e:
            print(f"⚠ 获取推荐颜色失败: {e}，使用默认颜色")
            from config.themes import DEFAULT_THEME, BASE_COLOR_SCHEMES, generate_random_color_variant

            # 生成默认颜色方案
            default_scheme = generate_random_color_variant(BASE_COLOR_SCHEMES[DEFAULT_THEME])
            scheme_name = f"{university}_{college}_默认主题"

            from config.themes import THEME_COLORS
            THEME_COLORS[scheme_name] = default_scheme

            return scheme_name
    
    def get_university_info(self, university: str) -> Dict:
        """
        获取某大学的完整信息
        
        Args:
            university: 大学名称
            
        Returns:
            大学信息字典
        """
        clean_university = self._clean_university_name(university)
        return self.data.get(clean_university, {})
    
    def get_all_universities(self) -> List[str]:
        """获取所有记录的大学名称"""
        return list(self.data.keys())
    
    def get_statistics(self) -> Dict:
        """获取数据库统计信息"""
        total_universities = len(self.data)
        total_colleges = sum(len(colleges) for colleges in self.data.values())
        
        # 统计颜色使用频率
        color_usage = {}
        for university_data in self.data.values():
            for college_info in university_data.values():
                if isinstance(college_info, dict) and 'theme_color' in college_info:
                    color = college_info['theme_color']
                    color_usage[color] = color_usage.get(color, 0) + 1
        
        return {
            'total_universities': total_universities,
            'total_colleges': total_colleges,
            'color_usage': color_usage,
            'most_used_color': max(color_usage.items(), key=lambda x: x[1])[0] if color_usage else None
        }
    
    def clear_university_data(self, university: str) -> bool:
        """
        清除某大学的所有数据
        
        Args:
            university: 大学名称
            
        Returns:
            是否清除成功
        """
        try:
            clean_university = self._clean_university_name(university)
            if clean_university in self.data:
                del self.data[clean_university]
                success = self._save_database()
                if success:
                    print(f"✓ 清除大学数据: {clean_university}")
                return success
            else:
                print(f"⚠ 大学 {clean_university} 不存在于数据库中")
                return True
        except Exception as e:
            print(f"⚠ 清除大学数据失败: {e}")
            return False
    
    def _clean_university_name(self, university: str) -> str:
        """清理大学名称，统一格式"""
        # 移除多余的空格
        clean_name = university.strip()
        
        # 确保以"大学"结尾的统一格式
        if '大学' in clean_name and not clean_name.endswith('大学'):
            # 提取大学名称部分
            univ_index = clean_name.find('大学')
            clean_name = clean_name[:univ_index + 2]
        
        return clean_name
    
    def _get_timestamp(self) -> str:
        """获取当前时间戳"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")


def test_color_database():
    """测试颜色数据库功能"""
    try:
        db = ColorDatabase()
        
        # 测试记录颜色使用
        db.record_color_usage("电子科技大学", "经济与管理学院", "科技蓝")
        db.record_color_usage("电子科技大学", "计算机科学与工程学院", "科技蓝")  # 应该被替换
        
        # 测试获取推荐颜色
        recommended = db.get_recommended_color("电子科技大学", "信息与软件工程学院", "科技蓝")
        print(f"推荐颜色: {recommended}")
        
        # 测试统计信息
        stats = db.get_statistics()
        print(f"统计信息: {stats}")
        
        print("颜色数据库测试完成")
        
    except Exception as e:
        print(f"测试失败: {e}")


if __name__ == "__main__":
    test_color_database()
