#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DeepSeek API客户端模块
用于智能识别学院名称并推荐主题色
"""

import json
import time
from typing import Dict, Optional, Tuple
from openai import OpenAI

from config.settings import APP_CONFIG, DEEPSEEK_SYSTEM_PROMPT
from config.themes import get_all_theme_names, DEFAULT_THEME
from .color_database import ColorDatabase


class DeepSeekClient:
    """DeepSeek API客户端"""
    
    def __init__(self):
        self.config = APP_CONFIG['deepseek_api']
        self.client = OpenAI(
            api_key=self.config['api_key'],
            base_url=self.config['base_url']
        )
        self.available_themes = get_all_theme_names()
        self.color_db = ColorDatabase()
        
    def analyze_college_name(self, college_name: str) -> Dict[str, str]:
        """
        分析学院名称，提取大学名称、学院名称并推荐主题色，注意，主题色必须契合学校和学院特色
        
        Args:
            college_name: 完整的学院名称，如"电子科技大学经济与管理学院"
            
        Returns:
            包含university, college, theme_color的字典
        """
        try:
            # 构建用户提示词
            user_prompt = f"""
请分析以下学院名称："{college_name}"

可选的主题色包括：{', '.join(self.available_themes)}

请严格按照JSON格式回复，不要添加任何其他内容。
"""
            
            print(f"正在分析学院名称: {college_name}")
            
            # 调用API
            response = self._call_api(user_prompt)
            
            if response:
                # 验证和修正返回结果
                validated_result = self._validate_response(response, college_name)

                # 使用颜色数据库优化颜色选择
                university = validated_result['university']
                college = validated_result['college']
                ai_suggested_color = validated_result['theme_color']

                # 获取推荐颜色（避免同校重复）
                recommended_color = self.color_db.get_recommended_color(
                    university, college, ai_suggested_color)
                validated_result['theme_color'] = recommended_color

                # 记录颜色使用
                self.color_db.record_color_usage(university, college, recommended_color)

                print(f"分析结果: {validated_result}")
                return validated_result
            else:
                # API调用失败，使用备用方案
                return self._fallback_analysis(college_name)
                
        except Exception as e:
            print(f"API分析失败: {e}")
            return self._fallback_analysis(college_name)
    
    def _call_api(self, user_prompt: str) -> Optional[Dict]:
        """调用DeepSeek API"""
        try:
            response = self.client.chat.completions.create(
                model=self.config['model'],
                messages=[
                    {"role": "system", "content": DEEPSEEK_SYSTEM_PROMPT},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=0.1,  # 降低随机性，提高一致性
                max_tokens=200,
                timeout=self.config['timeout']
            )
            
            # 提取回复内容
            content = response.choices[0].message.content.strip()
            print(f"API原始回复: {content}")
            
            # 尝试解析JSON
            try:
                # 清理可能的markdown格式
                if content.startswith('```json'):
                    content = content.replace('```json', '').replace('```', '').strip()
                elif content.startswith('```'):
                    content = content.replace('```', '').strip()
                
                result = json.loads(content)
                return result
                
            except json.JSONDecodeError as e:
                print(f"JSON解析失败: {e}")
                print(f"原始内容: {content}")
                
                # 尝试从文本中提取信息
                return self._extract_from_text(content)
                
        except Exception as e:
            print(f"API调用失败: {e}")
            return None
    
    def _extract_from_text(self, text: str) -> Optional[Dict]:
        """从文本中提取结构化信息（备用解析方法）"""
        try:
            result = {}
            
            # 查找大学名称
            for line in text.split('\n'):
                line = line.strip()
                if 'university' in line.lower() or '大学' in line:
                    # 提取引号或冒号后的内容
                    if ':' in line:
                        result['university'] = line.split(':', 1)[1].strip().strip('"\'')
                    elif '"' in line:
                        parts = line.split('"')
                        if len(parts) >= 2:
                            result['university'] = parts[1].strip()
                
                elif 'college' in line.lower() or '学院' in line:
                    if ':' in line:
                        result['college'] = line.split(':', 1)[1].strip().strip('"\'')
                    elif '"' in line:
                        parts = line.split('"')
                        if len(parts) >= 2:
                            result['college'] = parts[1].strip()
                
                elif 'theme' in line.lower() or '主题' in line:
                    if ':' in line:
                        theme = line.split(':', 1)[1].strip().strip('"\'')
                        if theme in self.available_themes:
                            result['theme_color'] = theme
            
            if len(result) >= 2:  # 至少要有两个字段
                return result
            
        except Exception as e:
            print(f"文本提取失败: {e}")
        
        return None
    
    def _validate_response(self, response: Dict, original_name: str) -> Dict[str, str]:
        """验证和修正API响应"""
        result = {
            'university': '',
            'college': '',
            'theme_color': DEFAULT_THEME
        }
        
        # 验证大学名称
        if 'university' in response and response['university']:
            result['university'] = str(response['university']).strip()
        
        # 验证学院名称
        if 'college' in response and response['college']:
            result['college'] = str(response['college']).strip()
        
        # 验证主题色
        if 'theme_color' in response and response['theme_color'] in self.available_themes:
            result['theme_color'] = response['theme_color']
        else:
            # 如果主题色无效，根据学院名称智能推测
            result['theme_color'] = self._guess_theme_by_name(original_name)
        
        # 如果大学或学院名称为空，尝试从原始名称中提取
        if not result['university'] or not result['college']:
            fallback = self._extract_names_from_original(original_name)
            if not result['university']:
                result['university'] = fallback['university']
            if not result['college']:
                result['college'] = fallback['college']
        
        return result
    
    def _fallback_analysis(self, college_name: str) -> Dict[str, str]:
        """备用分析方案（当API不可用时）"""
        print("使用备用分析方案")

        # 提取大学和学院名称
        names = self._extract_names_from_original(college_name)

        # 推测主题色
        ai_suggested_theme = self._guess_theme_by_name(college_name)

        # 使用颜色数据库优化颜色选择
        university = names['university']
        college = names['college']

        # 获取推荐颜色（避免同校重复）
        recommended_color = self.color_db.get_recommended_color(
            university, college, ai_suggested_theme)

        # 记录颜色使用
        self.color_db.record_color_usage(university, college, recommended_color)

        return {
            'university': university,
            'college': college,
            'theme_color': recommended_color
        }
    
    def _extract_names_from_original(self, college_name: str) -> Dict[str, str]:
        """从原始名称中提取大学和学院名称"""
        university = ""
        college = ""
        
        # 查找大学名称
        if "大学" in college_name:
            # 找到"大学"的位置
            univ_end = college_name.find("大学") + 2
            university = college_name[:univ_end]
            
            # 剩余部分作为学院名称
            remaining = college_name[univ_end:].strip()
            if remaining:
                college = remaining
            else:
                college = "未知学院"
        else:
            # 如果没有"大学"，尝试其他模式
            if "学院" in college_name:
                college = college_name
                university = "未知大学"
            else:
                university = college_name
                college = "未知学院"
        
        return {
            'university': university,
            'college': college
        }
    
    def _guess_theme_by_name(self, college_name: str) -> str:
        """根据学院名称推测主题色"""
        name_lower = college_name.lower()
        
        # 关键词匹配
        if any(keyword in name_lower for keyword in ['计算机', '电子', '信息', '软件', '网络', '科技']):
            return '科技蓝'
        elif any(keyword in name_lower for keyword in ['商学', '经济', '管理', '金融', '会计', '工商']):
            return '商务金'
        elif any(keyword in name_lower for keyword in ['文学', '教育', '人文', '社会', '历史', '哲学']):
            return '学术绿'
        elif any(keyword in name_lower for keyword in ['医学', '生命', '生物', '药学', '护理']):
            return '医学红'
        elif any(keyword in name_lower for keyword in ['法学', '政治', '公共', '行政', '法律']):
            return '法政紫'
        elif any(keyword in name_lower for keyword in ['艺术', '设计', '美术', '音乐', '传媒']):
            return '艺术橙'
        elif any(keyword in name_lower for keyword in ['工程', '建筑', '土木', '机械', '材料']):
            return '工程灰'
        else:
            return DEFAULT_THEME
    
    def test_connection(self) -> bool:
        """测试API连接"""
        try:
            self.client.chat.completions.create(
                model=self.config['model'],
                messages=[
                    {"role": "user", "content": "Hello"}
                ],
                max_tokens=10,
                timeout=10
            )
            return True
        except Exception as e:
            print(f"API连接测试失败: {e}")
            return False
