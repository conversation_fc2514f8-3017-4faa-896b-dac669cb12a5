#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel文件读取模块
用于读取Excel文件中的学院名称数据
"""

import os
import pandas as pd
from typing import List, Optional


class ExcelReader:
    """Excel文件读取器"""
    
    def __init__(self):
        self.supported_formats = ['.xlsx', '.xls', '.csv']
    
    def read_college_names(self, file_path: str, column_index: int = 0) -> List[str]:
        """
        从Excel文件中读取学院名称列表
        
        Args:
            file_path: Excel文件路径
            column_index: 列索引，默认为0（第一列）
            
        Returns:
            学院名称列表
        """
        try:
            # 检查文件是否存在
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"文件不存在: {file_path}")
            
            # 检查文件格式
            file_ext = os.path.splitext(file_path)[1].lower()
            if file_ext not in self.supported_formats:
                raise ValueError(f"不支持的文件格式: {file_ext}，支持的格式: {', '.join(self.supported_formats)}")
            
            print(f"正在读取文件: {file_path}")
            
            # 根据文件类型读取数据
            if file_ext == '.csv':
                df = pd.read_csv(file_path, encoding='utf-8-sig')
            else:
                df = pd.read_excel(file_path, engine='openpyxl' if file_ext == '.xlsx' else 'xlrd')
            
            # 检查数据是否为空
            if df.empty:
                raise ValueError("Excel文件为空")
            
            # 检查列索引是否有效
            if column_index >= len(df.columns):
                raise ValueError(f"列索引 {column_index} 超出范围，文件只有 {len(df.columns)} 列")
            
            # 获取指定列的数据
            column_data = df.iloc[:, column_index]
            
            # 清理数据：去除空值和重复值
            college_names = []
            for value in column_data:
                if pd.notna(value):  # 排除NaN值
                    name = str(value).strip()
                    if name and name not in college_names:  # 排除空字符串和重复值
                        college_names.append(name)
            
            print(f"成功读取 {len(college_names)} 个学院名称")
            
            # 显示前几个示例
            if college_names:
                print("示例数据:")
                for i, name in enumerate(college_names[:5]):
                    print(f"  {i+1}. {name}")
                if len(college_names) > 5:
                    print(f"  ... 还有 {len(college_names) - 5} 个")
            
            return college_names
            
        except Exception as e:
            print(f"读取Excel文件失败: {e}")
            raise
    
    def validate_file(self, file_path: str) -> bool:
        """
        验证文件是否可以正常读取
        
        Args:
            file_path: 文件路径
            
        Returns:
            是否可以正常读取
        """
        try:
            # 检查文件是否存在
            if not os.path.exists(file_path):
                return False
            
            # 检查文件格式
            file_ext = os.path.splitext(file_path)[1].lower()
            if file_ext not in self.supported_formats:
                return False
            
            # 尝试读取文件
            if file_ext == '.csv':
                df = pd.read_csv(file_path, encoding='utf-8-sig', nrows=1)
            else:
                df = pd.read_excel(file_path, nrows=1, engine='openpyxl' if file_ext == '.xlsx' else 'xlrd')
            
            return not df.empty
            
        except Exception as e:
            print(f"文件验证失败: {e}")
            return False
    
    def get_file_info(self, file_path: str) -> Optional[dict]:
        """
        获取文件基本信息
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件信息字典
        """
        try:
            if not os.path.exists(file_path):
                return None
            
            file_ext = os.path.splitext(file_path)[1].lower()
            if file_ext not in self.supported_formats:
                return None
            
            # 读取文件
            if file_ext == '.csv':
                df = pd.read_csv(file_path, encoding='utf-8-sig')
            else:
                df = pd.read_excel(file_path, engine='openpyxl' if file_ext == '.xlsx' else 'xlrd')
            
            # 统计信息
            info = {
                'file_name': os.path.basename(file_path),
                'file_size': os.path.getsize(file_path),
                'rows': len(df),
                'columns': len(df.columns),
                'column_names': df.columns.tolist() if not df.empty else [],
                'first_column_sample': []
            }
            
            # 获取第一列的示例数据
            if not df.empty and len(df.columns) > 0:
                first_column = df.iloc[:, 0]
                sample_data = []
                for value in first_column.head(10):
                    if pd.notna(value):
                        sample_data.append(str(value).strip())
                info['first_column_sample'] = sample_data
            
            return info
            
        except Exception as e:
            print(f"获取文件信息失败: {e}")
            return None
    
    def preview_data(self, file_path: str, rows: int = 10) -> Optional[pd.DataFrame]:
        """
        预览文件数据
        
        Args:
            file_path: 文件路径
            rows: 预览行数
            
        Returns:
            预览数据DataFrame
        """
        try:
            if not os.path.exists(file_path):
                return None
            
            file_ext = os.path.splitext(file_path)[1].lower()
            if file_ext not in self.supported_formats:
                return None
            
            # 读取指定行数的数据
            if file_ext == '.csv':
                df = pd.read_csv(file_path, encoding='utf-8-sig', nrows=rows)
            else:
                df = pd.read_excel(file_path, nrows=rows, engine='openpyxl' if file_ext == '.xlsx' else 'xlrd')
            
            return df
            
        except Exception as e:
            print(f"预览数据失败: {e}")
            return None


def test_excel_reader():
    """测试Excel读取器"""
    reader = ExcelReader()
    
    # 创建测试数据
    test_data = {
        '学院名称': [
            '西南财经大学管理科学与工程学院',
            '中南财经政法大学经济学院',
            '中南财经政法大学财政税务学院',
            '中南财经政法大学会计学院',
            '北京外国语大学国际商学院',
            '上海外国语大学国际商务',
            '上海外国语大学数字经济',
            '北京理工大学经济学院',
            '西南财经大学经济学院',
            '大连理工大学公共经管理学院',
            '江南大学商学院',
            '西北大学MPA教育中心'
        ]
    }
    
    # 创建测试Excel文件
    test_file = 'test_colleges.xlsx'
    df = pd.DataFrame(test_data)
    df.to_excel(test_file, index=False)
    
    try:
        # 测试读取
        college_names = reader.read_college_names(test_file)
        print(f"测试成功，读取到 {len(college_names)} 个学院名称")
        
        # 测试文件信息
        info = reader.get_file_info(test_file)
        if info:
            print(f"文件信息: {info}")
        
    finally:
        # 清理测试文件
        if os.path.exists(test_file):
            os.remove(test_file)


if __name__ == "__main__":
    test_excel_reader()
