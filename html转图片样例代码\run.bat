@echo off
chcp 65001 > nul
title HTML转图片工具

echo ================================
echo   HTML转图片工具 v2.0
echo ================================
echo.

cd /d "%~dp0"

:: 检查Python是否安装
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 未找到Python，请先安装Python 3.8+
    pause
    exit /b 1
)

:: 启动主程序
echo 正在启动程序...
python main.py

if %errorlevel% neq 0 (
    echo.
    echo [错误] 程序运行失败，错误代码: %errorlevel%
    echo.
    echo 可能的解决方案：
    echo 1. 运行 install_dependencies.bat 安装依赖
    echo 2. 检查Python版本是否为3.8+
    echo 3. 查看上方错误信息
)

pause
