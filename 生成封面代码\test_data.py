#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建测试数据
"""

import pandas as pd
import os

def create_test_excel():
    """创建测试Excel文件"""
    
    # 测试数据
    test_colleges = [
        "西南财经大学管理科学与工程学院",
        "中南财经政法大学经济学院", 
        "中南财经政法大学财政税务学院",
        "中南财经政法大学会计学院",
        "北京外国语大学国际商学院",
        "上海外国语大学国际商务",
        "上海外国语大学数字经济",
        "北京理工大学经济学院",
        "西南财经大学经济学院",
        "大连理工大学公共经管理学院",
        "江南大学商学院",
        "西北大学MPA教育中心",
        "清华大学经济管理学院",
        "北京大学光华管理学院",
        "复旦大学管理学院",
        "上海交通大学安泰经济与管理学院",
        "浙江大学管理学院",
        "南京大学商学院",
        "中山大学管理学院",
        "华中科技大学管理学院"
    ]
    
    # 创建DataFrame
    df = pd.DataFrame({
        '学院名称': test_colleges
    })
    
    # 保存为Excel文件
    output_path = os.path.join(os.path.dirname(__file__), 'test_colleges.xlsx')
    df.to_excel(output_path, index=False)
    
    print(f"测试Excel文件已创建: {output_path}")
    print(f"包含 {len(test_colleges)} 个学院名称")
    
    return output_path

if __name__ == "__main__":
    create_test_excel()
