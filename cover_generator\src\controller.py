"""
封面生成器核心控制器
协调各个模块完成封面生成任务
"""

import os
import threading
import time
from typing import Dict, List, Optional, Callable
from .deepseek_client import deepseek_client
from .html_generator import html_generator
from .image_converter import html_to_image_converter
from .file_manager import file_manager
from .excel_processor import excel_processor

class CoverGenerator:
    def __init__(self):
        self.is_processing = False
        self.current_progress = 0
        self.current_message = ""
    
    def generate_single_cover(self, school_text: str, 
                            progress_callback: Optional[Callable] = None,
                            success_callback: Optional[Callable] = None,
                            error_callback: Optional[Callable] = None) -> bool:
        """
        生成单个学校的封面
        
        Args:
            school_text: 学校学院文本
            progress_callback: 进度回调 (progress, message)
            success_callback: 成功回调 (html_path, png_path)
            error_callback: 错误回调 (error_message)
        
        Returns:
            bool: 是否成功
        """
        try:
            if progress_callback:
                progress_callback(10, "正在分析学校信息...")
            
            # 1. 分析学校信息
            school_info = deepseek_client.analyze_school_info(school_text)
            print(f"分析结果: {school_info}")
            
            if progress_callback:
                progress_callback(30, "正在生成HTML模板...")
            
            # 2. 生成HTML内容
            html_content = html_generator.generate_html(school_info)
            
            if progress_callback:
                progress_callback(50, "正在创建输出文件夹...")
            
            # 3. 创建输出文件夹
            school_folder = file_manager.create_school_folder(school_info)
            html_path = file_manager.get_html_path(school_folder, school_info)
            png_path = file_manager.get_png_path(school_folder, school_info)
            
            if progress_callback:
                progress_callback(60, "正在保存HTML文件...")
            
            # 4. 保存HTML文件
            if not file_manager.save_html_file(html_content, html_path):
                raise RuntimeError("保存HTML文件失败")
            
            if progress_callback:
                progress_callback(70, "正在转换为PNG图片...")
            
            # 5. 转换为PNG图片
            def png_progress(prog, msg):
                if progress_callback:
                    # 将PNG转换进度映射到70-100%
                    total_progress = 70 + (prog * 30 // 100)
                    progress_callback(total_progress, f"PNG转换: {msg}")
            
            success = html_to_image_converter.convert_html_to_png(
                html_content, png_path, png_progress
            )
            
            if not success:
                raise RuntimeError("PNG转换失败")
            
            if progress_callback:
                progress_callback(100, "封面生成完成")
            
            if success_callback:
                success_callback(html_path, png_path)
            
            return True
            
        except Exception as e:
            error_msg = f"生成封面失败: {str(e)}"
            print(error_msg)
            if error_callback:
                error_callback(error_msg)
            return False
    
    def generate_batch_covers(self, school_list: List[str],
                            progress_callback: Optional[Callable] = None,
                            item_callback: Optional[Callable] = None,
                            complete_callback: Optional[Callable] = None,
                            error_callback: Optional[Callable] = None) -> None:
        """
        批量生成封面
        
        Args:
            school_list: 学校学院列表
            progress_callback: 总体进度回调 (current, total, message)
            item_callback: 单项完成回调 (index, school_name, success, paths)
            complete_callback: 完成回调 (success_count, total_count)
            error_callback: 错误回调 (error_message)
        """
        def batch_process():
            try:
                self.is_processing = True
                total_count = len(school_list)
                success_count = 0
                
                if progress_callback:
                    progress_callback(0, total_count, "开始批量处理...")
                
                for i, school_text in enumerate(school_list):
                    try:
                        if progress_callback:
                            progress_callback(i, total_count, f"正在处理: {school_text}")
                        
                        # 生成单个封面
                        def single_progress(prog, msg):
                            if progress_callback:
                                progress_callback(i, total_count, f"{school_text}: {msg}")
                        
                        def single_success(html_path, png_path):
                            nonlocal success_count
                            success_count += 1
                            if item_callback:
                                item_callback(i, school_text, True, (html_path, png_path))
                        
                        def single_error(error_msg):
                            if item_callback:
                                item_callback(i, school_text, False, error_msg)
                        
                        self.generate_single_cover(
                            school_text, single_progress, single_success, single_error
                        )
                        
                        # 短暂延迟，避免API调用过于频繁
                        time.sleep(0.5)
                        
                    except Exception as e:
                        print(f"处理 {school_text} 时出错: {e}")
                        if item_callback:
                            item_callback(i, school_text, False, str(e))
                
                # 完成处理
                if progress_callback:
                    progress_callback(total_count, total_count, "批量处理完成")
                
                if complete_callback:
                    complete_callback(success_count, total_count)
                
            except Exception as e:
                if error_callback:
                    error_callback(f"批量处理失败: {str(e)}")
            finally:
                self.is_processing = False
        
        # 在新线程中执行批量处理
        thread = threading.Thread(target=batch_process, daemon=True)
        thread.start()
        return thread
    
    def process_excel_file(self, excel_path: str,
                          progress_callback: Optional[Callable] = None,
                          item_callback: Optional[Callable] = None,
                          complete_callback: Optional[Callable] = None,
                          error_callback: Optional[Callable] = None) -> None:
        """
        处理Excel文件并批量生成封面
        """
        try:
            if progress_callback:
                progress_callback(0, 0, "正在读取Excel文件...")
            
            # 读取Excel文件
            school_list = excel_processor.read_school_list(excel_path)
            
            if not school_list:
                if error_callback:
                    error_callback("Excel文件中没有有效的学校信息")
                return
            
            print(f"从Excel读取到 {len(school_list)} 条学校信息")
            
            # 开始批量处理
            self.generate_batch_covers(
                school_list, progress_callback, item_callback, 
                complete_callback, error_callback
            )
            
        except Exception as e:
            if error_callback:
                error_callback(f"处理Excel文件失败: {str(e)}")
    
    def test_api_connection(self) -> bool:
        """
        测试API连接
        """
        try:
            return deepseek_client.test_connection()
        except Exception as e:
            print(f"API连接测试失败: {e}")
            return False
    
    def get_output_summary(self) -> Dict:
        """
        获取输出摘要
        """
        return file_manager.get_output_summary()
    
    def open_output_folder(self) -> bool:
        """
        打开输出文件夹
        """
        return file_manager.open_output_folder()

# 创建全局实例
cover_generator = CoverGenerator()
