#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件管理模块
用于标准化文件命名和文件夹组织
"""

import os
import re
import shutil
from typing import Dict, List, Optional, Tuple
from datetime import datetime

from config.settings import APP_CONFIG


class FileManager:
    """文件管理器"""
    
    def __init__(self):
        self.config = APP_CONFIG
        self.output_dir = self.config['output_dir']
        self.naming_config = self.config['naming']
        
        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)
    
    def sanitize_filename(self, filename: str) -> str:
        """
        清理文件名，移除不合法字符
        
        Args:
            filename: 原始文件名
            
        Returns:
            清理后的文件名
        """
        # 移除或替换不合法字符
        illegal_chars = r'[<>:"/\\|?*]'
        filename = re.sub(illegal_chars, '_', filename)
        
        # 移除多余的空格和点
        filename = re.sub(r'\s+', '_', filename.strip())
        filename = filename.strip('.')
        
        # 限制长度
        if len(filename) > 100:
            filename = filename[:100]
        
        return filename
    
    def generate_folder_name(self, university: str, college: str) -> str:
        """
        生成文件夹名称
        
        Args:
            university: 大学名称
            college: 学院名称
            
        Returns:
            文件夹名称
        """
        # 清理名称
        clean_university = self.sanitize_filename(university)
        clean_college = self.sanitize_filename(college)
        
        # 生成文件夹名称
        folder_name = self.naming_config['folder_format'].format(
            university=clean_university,
            college=clean_college
        )
        
        return folder_name
    
    def generate_file_name(self, university: str, college: str, suffix: str) -> str:
        """
        生成文件名称
        
        Args:
            university: 大学名称
            college: 学院名称
            suffix: 文件后缀（如"封面设计.html"）
            
        Returns:
            文件名称
        """
        # 清理名称
        clean_university = self.sanitize_filename(university)
        clean_college = self.sanitize_filename(college)
        
        # 生成文件名称
        file_name = self.naming_config['file_format'].format(
            university=clean_university,
            college=clean_college,
            suffix=suffix
        )
        
        return file_name
    
    def create_college_folder(self, university: str, college: str) -> str:
        """
        创建学院专用文件夹
        
        Args:
            university: 大学名称
            college: 学院名称
            
        Returns:
            创建的文件夹路径
        """
        folder_name = self.generate_folder_name(university, college)
        folder_path = os.path.join(self.output_dir, folder_name)
        
        # 创建文件夹
        os.makedirs(folder_path, exist_ok=True)
        
        print(f"✓ 创建文件夹: {folder_path}")
        return folder_path
    
    def get_output_paths(self, university: str, college: str) -> Dict[str, str]:
        """
        获取输出文件路径
        
        Args:
            university: 大学名称
            college: 学院名称
            
        Returns:
            包含HTML和PNG文件路径的字典
        """
        # 创建文件夹
        folder_path = self.create_college_folder(university, college)
        
        # 生成文件名
        html_filename = self.generate_file_name(university, college, self.naming_config['html_suffix'])
        image_filename = self.generate_file_name(university, college, self.naming_config['image_suffix'])
        
        # 生成完整路径
        paths = {
            'folder': folder_path,
            'html': os.path.join(folder_path, html_filename),
            'image': os.path.join(folder_path, image_filename)
        }
        
        return paths
    
    def create_batch_folder(self, batch_name: Optional[str] = None) -> str:
        """
        创建批量处理文件夹
        
        Args:
            batch_name: 批次名称，如果为None则使用时间戳
            
        Returns:
            批量处理文件夹路径
        """
        if not batch_name:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            batch_name = f"批量生成_{timestamp}"
        
        batch_folder = os.path.join(self.output_dir, self.sanitize_filename(batch_name))
        os.makedirs(batch_folder, exist_ok=True)
        
        print(f"✓ 创建批量处理文件夹: {batch_folder}")
        return batch_folder
    
    def get_batch_output_paths(self, university: str, college: str, batch_folder: str) -> Dict[str, str]:
        """
        获取批量处理的输出文件路径
        
        Args:
            university: 大学名称
            college: 学院名称
            batch_folder: 批量处理文件夹路径
            
        Returns:
            包含HTML和PNG文件路径的字典
        """
        # 在批量文件夹下创建学院子文件夹
        folder_name = self.generate_folder_name(university, college)
        college_folder = os.path.join(batch_folder, folder_name)
        os.makedirs(college_folder, exist_ok=True)
        
        # 生成文件名
        html_filename = self.generate_file_name(university, college, self.naming_config['html_suffix'])
        image_filename = self.generate_file_name(university, college, self.naming_config['image_suffix'])
        
        # 生成完整路径
        paths = {
            'folder': college_folder,
            'html': os.path.join(college_folder, html_filename),
            'image': os.path.join(college_folder, image_filename)
        }
        
        return paths
    
    def cleanup_temp_files(self, folder_path: str, keep_patterns: List[str] = None) -> int:
        """
        清理临时文件
        
        Args:
            folder_path: 文件夹路径
            keep_patterns: 要保留的文件模式列表
            
        Returns:
            清理的文件数量
        """
        if not os.path.exists(folder_path):
            return 0
        
        keep_patterns = keep_patterns or ['*.html', '*.png', '*.jpg', '*.jpeg']
        cleaned_count = 0
        
        try:
            for root, dirs, files in os.walk(folder_path):
                for file in files:
                    file_path = os.path.join(root, file)
                    
                    # 检查是否需要保留
                    should_keep = False
                    for pattern in keep_patterns:
                        if file.endswith(pattern.replace('*', '')):
                            should_keep = True
                            break
                    
                    # 删除临时文件
                    if not should_keep and (file.startswith('temp_') or file.endswith('.tmp')):
                        try:
                            os.remove(file_path)
                            cleaned_count += 1
                        except Exception as e:
                            print(f"清理文件失败 {file_path}: {e}")
            
            if cleaned_count > 0:
                print(f"✓ 清理了 {cleaned_count} 个临时文件")
            
        except Exception as e:
            print(f"清理临时文件失败: {e}")
        
        return cleaned_count
    
    def get_folder_info(self, folder_path: str) -> Optional[Dict]:
        """
        获取文件夹信息
        
        Args:
            folder_path: 文件夹路径
            
        Returns:
            文件夹信息字典
        """
        try:
            if not os.path.exists(folder_path):
                return None
            
            # 统计文件
            html_files = []
            image_files = []
            other_files = []
            total_size = 0
            
            for root, dirs, files in os.walk(folder_path):
                for file in files:
                    file_path = os.path.join(root, file)
                    file_size = os.path.getsize(file_path)
                    total_size += file_size
                    
                    if file.endswith('.html'):
                        html_files.append(file)
                    elif file.endswith(('.png', '.jpg', '.jpeg')):
                        image_files.append(file)
                    else:
                        other_files.append(file)
            
            info = {
                'folder_path': folder_path,
                'html_count': len(html_files),
                'image_count': len(image_files),
                'other_count': len(other_files),
                'total_files': len(html_files) + len(image_files) + len(other_files),
                'total_size': total_size,
                'html_files': html_files,
                'image_files': image_files,
                'other_files': other_files
            }
            
            return info
            
        except Exception as e:
            print(f"获取文件夹信息失败: {e}")
            return None
    
    def open_folder(self, folder_path: str) -> bool:
        """
        打开文件夹
        
        Args:
            folder_path: 文件夹路径
            
        Returns:
            是否成功打开
        """
        try:
            if not os.path.exists(folder_path):
                print(f"文件夹不存在: {folder_path}")
                return False
            
            import platform
            system = platform.system()
            
            if system == "Windows":
                os.startfile(folder_path)
            elif system == "Darwin":  # macOS
                os.system(f"open '{folder_path}'")
            else:  # Linux
                os.system(f"xdg-open '{folder_path}'")
            
            print(f"✓ 已打开文件夹: {folder_path}")
            return True
            
        except Exception as e:
            print(f"打开文件夹失败: {e}")
            return False
    
    def backup_files(self, source_folder: str, backup_name: Optional[str] = None) -> Optional[str]:
        """
        备份文件
        
        Args:
            source_folder: 源文件夹
            backup_name: 备份名称
            
        Returns:
            备份文件夹路径
        """
        try:
            if not os.path.exists(source_folder):
                return None
            
            if not backup_name:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_name = f"backup_{timestamp}"
            
            backup_folder = os.path.join(self.output_dir, f"备份_{backup_name}")
            
            # 复制文件夹
            shutil.copytree(source_folder, backup_folder)
            
            print(f"✓ 文件备份完成: {backup_folder}")
            return backup_folder
            
        except Exception as e:
            print(f"文件备份失败: {e}")
            return None


def test_file_manager():
    """测试文件管理器"""
    try:
        manager = FileManager()
        
        # 测试文件名清理
        test_name = "电子科技大学<>经济与管理学院"
        clean_name = manager.sanitize_filename(test_name)
        print(f"文件名清理测试: '{test_name}' -> '{clean_name}'")
        
        # 测试路径生成
        paths = manager.get_output_paths("电子科技大学", "经济与管理学院")
        print(f"输出路径: {paths}")
        
        # 测试文件夹信息
        info = manager.get_folder_info(manager.output_dir)
        if info:
            print(f"输出目录信息: {info}")
        
        print("文件管理器测试完成")
        
    except Exception as e:
        print(f"测试失败: {e}")


if __name__ == "__main__":
    test_file_manager()
