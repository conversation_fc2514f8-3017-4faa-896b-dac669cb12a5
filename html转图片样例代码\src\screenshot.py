"""
HTML转图片/PDF工具 - 转换处理模块
支持HTML转图片(截图)和HTML转PDF(直接转换)
修复超时和加载问题
"""

import os
import time
import threading
import tempfile
from playwright.sync_api import sync_playwright
from .config import DEFAULT_CONFIG
from .image_processor import ImageProcessor
from .pdf_processor import PDFProcessor


class ScreenshotCapture:
    """HTML转换处理类 - 支持转图片和转PDF"""
    
    def __init__(self, config=None):
        self.config = config or DEFAULT_CONFIG
        self.image_processor = ImageProcessor(
            crop_threshold=self.config.get('crop_threshold', 240),
            crop_padding=self.config.get('crop_padding', 20)
        )
        self.pdf_processor = PDFProcessor()
    
    def capture_html_screenshot(self, html_path, save_path, quality_settings, 
                              format_type, screenshot_type="full_page", 
                              enable_vector=True, anti_aliasing=True, 
                              auto_crop=True, callback=None, error_callback=None,
                              progress_callback=None):
        """
        处理HTML转换请求
        
        Args:
            html_path: HTML文件路径
            save_path: 保存路径
            quality_settings: 质量设置
            format_type: 输出格式 (PNG/JPEG/WEBP/PDF)
            screenshot_type: 截图类型
            enable_vector: 是否启用矢量优化
            anti_aliasing: 是否启用抗锯齿
            auto_crop: 是否自动裁剪白边
            callback: 成功回调函数
            error_callback: 错误回调函数
            progress_callback: 进度回调函数
        """
        def conversion_thread():
            try:
                success = self._do_conversion(
                    html_path, save_path, quality_settings, format_type,
                    screenshot_type, enable_vector, anti_aliasing, auto_crop,
                    progress_callback
                )
                
                if success and callback:
                    callback(save_path)
                elif not success and error_callback:
                    error_callback("转换失败")
                    
            except Exception as e:
                if error_callback:
                    error_callback(str(e))
        
        # 在新线程中执行转换
        thread = threading.Thread(target=conversion_thread, daemon=True)
        thread.start()
        return thread
    
    def _preprocess_html(self, html_path):
        """
        预处理HTML文件，确保可以正常加载
        处理可能导致超时的问题
        """
        try:
            with open(html_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否需要预处理
            needs_processing = False
            
            # 如果HTML中包含外部资源加载，添加超时设置
            if 'http://' in content or 'https://' in content:
                needs_processing = True
                print("检测到外部资源，添加加载优化...")
                
                # 在head标签中添加资源加载优化
                head_optimization = '''
<style>
img, iframe, object, embed, video, audio {
    max-width: 100% !important;
    height: auto !important;
}
</style>
<script>
// 优化资源加载
document.addEventListener('DOMContentLoaded', function() {
    // 设置图片加载超时
    const images = document.querySelectorAll('img');
    images.forEach(img => {
        const timeout = setTimeout(() => {
            if (!img.complete) {
                img.style.display = 'none';
            }
        }, 5000);
        
        img.onload = () => clearTimeout(timeout);
        img.onerror = () => {
            clearTimeout(timeout);
            img.style.display = 'none';
        };
    });
});
</script>
'''
                
                if '<head>' in content:
                    content = content.replace('<head>', f'<head>{head_optimization}')
                elif '<html>' in content:
                    content = content.replace('<html>', f'<html><head>{head_optimization}</head>')
                else:
                    content = f'<head>{head_optimization}</head>{content}'
            
            # 如果需要预处理，创建临时文件
            if needs_processing:
                temp_dir = os.path.dirname(html_path)
                temp_file = tempfile.NamedTemporaryFile(
                    mode='w', 
                    encoding='utf-8', 
                    suffix='.html', 
                    dir=temp_dir, 
                    delete=False
                )
                temp_file.write(content)
                temp_file.close()
                print(f"创建优化的临时HTML文件: {temp_file.name}")
                return temp_file.name
            
            return html_path
            
        except Exception as e:
            print(f"HTML预处理失败: {e}，使用原文件")
            return html_path

    def _do_conversion(self, html_path, save_path, quality_settings, format_type,
                      screenshot_type, enable_vector, anti_aliasing, auto_crop,
                      progress_callback=None):
        """
        执行实际的转换操作
        """
        try:
            # 进度更新函数
            def update_progress(progress, message=""):
                if progress_callback:
                    progress_callback(progress, message)
            
            update_progress(5, "正在启动浏览器...")
            
            # 检查HTML文件是否存在
            if not os.path.exists(html_path):
                raise FileNotFoundError(f"HTML文件不存在: {html_path}")
            
            # 检查保存目录是否可写
            save_dir = os.path.dirname(save_path)
            if not save_dir:  # 相对路径的情况，使用当前目录
                save_dir = "."
            if not os.access(save_dir, os.W_OK):
                raise PermissionError(f"没有写入权限: {save_dir}")
            
            with sync_playwright() as p:
                # 构建浏览器启动参数
                browser_args = self.config['browser_args'].copy()
                browser_args.append(f'--force-device-scale-factor={quality_settings["scale"]}')
                
                if anti_aliasing:
                    browser_args.append('--enable-font-antialiasing')
                else:
                    browser_args.append('--disable-font-antialiasing')
                
                # 启动浏览器 - 优化大页面处理
                try:
                    browser = p.chromium.launch(
                        headless=True, 
                        args=browser_args,
                        # 增加资源限制以支持大页面
                        chromium_sandbox=False,
                        slow_mo=0,
                        timeout=120000  # 增加启动超时到2分钟
                    )
                    update_progress(15, "浏览器已启动，正在加载页面...")
                except Exception as e:
                    raise RuntimeError(f"无法启动浏览器: {str(e)}")
                
                try:
                    # 创建上下文 - 优化大页面处理
                    context = browser.new_context(
                        viewport={
                            'width': self.config['viewport']['width'],
                            'height': self.config['viewport']['height']
                        },
                        device_scale_factor=quality_settings["scale"],
                        # 增加对大页面的支持
                        java_script_enabled=True,
                        bypass_csp=True,  # 绕过内容安全策略
                        ignore_https_errors=True,
                        # 设置更大的超时
                        base_url=None,
                        extra_http_headers={}
                    )
                    
                    page = context.new_page()
                    
                    # 设置页面超时
                    page.set_default_timeout(120000)  # 2分钟超时
                    page.set_default_navigation_timeout(120000)
                    
                    # 设置媒体类型
                    if format_type == 'PDF':
                        page.emulate_media(media='print')
                        print("设置为打印媒体类型")
                    else:
                        page.emulate_media(media='screen')
                    
                    update_progress(25, "正在预处理HTML文件...")
                    
                    # 预处理HTML文件，确保可以正常加载
                    processed_html_path = self._preprocess_html(html_path)
                    
                    # 导航到HTML文件
                    file_url = f"file:///{os.path.abspath(processed_html_path).replace(os.sep, '/')}"
                    print(f"加载文件: {file_url}")
                    
                    try:
                        # 增加超时时间到60秒，并添加更多错误处理
                        page.goto(file_url, timeout=60000, wait_until='domcontentloaded')
                        print("DOM内容已加载，等待网络空闲...")
                        
                        # 等待网络空闲，但允许失败
                        try:
                            page.wait_for_load_state('networkidle', timeout=30000)
                            print("网络空闲状态达到")
                        except Exception as e:
                            print(f"网络空闲等待超时，继续处理: {e}")
                            # 即使网络空闲超时，也继续处理
                        
                    except Exception as e:
                        # 如果仍然超时，尝试简化的加载方式
                        print(f"标准加载失败: {e}")
                        try:
                            print("尝试简化加载方式...")
                            page.goto(file_url, timeout=30000, wait_until='commit')
                            time.sleep(3)  # 给页面更多时间加载
                            print("简化加载方式成功")
                        except Exception as e2:
                            raise RuntimeError(f"无法加载HTML文件: {str(e2)}")
                    
                    # 清理临时文件
                    if processed_html_path != html_path:
                        try:
                            os.remove(processed_html_path)
                        except:
                            pass
                    
                    update_progress(40, "页面加载完成，正在优化渲染...")
                    
                    # 等待内容加载
                    time.sleep(2)
                    
                    # 执行页面优化
                    if enable_vector:
                        try:
                            self._optimize_page_rendering(page)
                        except Exception as e:
                            print(f"页面优化警告: {e}")  # 优化失败不影响转换
                    
                    update_progress(60, f"正在生成{format_type}...")
                    
                    # 根据格式执行不同的转换操作
                    try:
                        if format_type == "PDF":
                            print(f"开始HTML转PDF: {html_path} -> {save_path}")
                            self._convert_to_pdf(page, save_path)
                        else:
                            print(f"开始HTML转{format_type}: {html_path} -> {save_path}")
                            self._capture_image(page, save_path, format_type, screenshot_type)
                    except Exception as e:
                        raise RuntimeError(f"转换失败: {str(e)}")
                    
                finally:
                    try:
                        browser.close()
                    except Exception:
                        pass  # 忽略浏览器关闭错误
                
                update_progress(80, "正在处理文件...")
                
                # 检查文件是否成功生成
                if not os.path.exists(save_path):
                    raise RuntimeError("输出文件未能成功生成")
                
                # 后处理
                if format_type == 'PDF':
                    # PDF后处理：强制裁剪白边
                    try:
                        print("正在强制裁剪PDF白边...")
                        success = self.pdf_processor.force_crop_pdf(save_path, margin=10)
                        if success:
                            print("✓ PDF白边强制裁剪完成")
                        else:
                            print("⚠ PDF白边裁剪失败，保持原样")
                    except Exception as e:
                        print(f"PDF白边裁剪警告: {e}")  # 裁剪失败不影响整体流程
                else:
                    # 图片后处理
                    # 自动裁剪白边
                    if auto_crop and format_type in ['PNG', 'JPEG', 'WEBP']:
                        try:
                            self.image_processor.auto_crop_white_borders(save_path)
                        except Exception as e:
                            print(f"图像裁剪警告: {e}")  # 裁剪失败不影响整体流程
                    
                    # 优化图片质量
                    try:
                        self.image_processor.optimize_image_quality(save_path, format_type)
                    except Exception as e:
                        print(f"图像优化警告: {e}")  # 优化失败不影响整体流程
                
                update_progress(100, "转换完成")
                print(f"✓ 转换成功: {save_path}")
                
                return True
                
        except FileNotFoundError as e:
            error_msg = f"文件错误: {str(e)}"
            print(error_msg)
            if progress_callback:
                progress_callback(0, error_msg)
            return False
        except PermissionError as e:
            error_msg = f"权限错误: {str(e)}"
            print(error_msg)
            if progress_callback:
                progress_callback(0, error_msg)
            return False
        except RuntimeError as e:
            error_msg = f"运行时错误: {str(e)}"
            print(error_msg)
            if progress_callback:
                progress_callback(0, error_msg)
            return False
        except Exception as e:
            error_msg = f"未知错误: {str(e)}\n类型: {type(e).__name__}"
            print(f"转换过程中出错: {error_msg}")
            if progress_callback:
                progress_callback(0, error_msg)
            return False
    
    def _optimize_page_rendering(self, page):
        """优化页面渲染 - 特别针对大页面"""
        try:
            page.evaluate("""
                (() => {
                    // 优化字体渲染
                    document.body.style.webkitFontSmoothing = 'antialiased';
                    document.body.style.mozOsxFontSmoothing = 'grayscale';
                    document.body.style.textRendering = 'optimizeLegibility';
                    
                    // 优化图像渲染
                    const images = document.querySelectorAll('img');
                    images.forEach(img => {
                        img.style.imageRendering = 'high-quality';
                        img.style.imageRendering = '-webkit-optimize-contrast';
                        // 确保图片加载完成
                        if (!img.complete && img.src) {
                            img.loading = 'eager';
                        }
                    });
                    
                    // 优化SVG渲染
                    const svgs = document.querySelectorAll('svg');
                    svgs.forEach(svg => {
                        svg.style.shapeRendering = 'geometricPrecision';
                    });
                    
                    // 优化大页面性能
                    document.body.style.willChange = 'auto';
                    document.body.style.backfaceVisibility = 'hidden';
                    document.body.style.perspective = '1000px';
                    
                    // 强制重绘以确保所有内容都已渲染
                    document.body.style.transform = 'translateZ(0)';
                    
                    // 等待所有图片加载完成
                    const allImages = Array.from(document.querySelectorAll('img'));
                    const imagePromises = allImages.map(img => {
                        if (img.complete) return Promise.resolve();
                        return new Promise((resolve, reject) => {
                            img.addEventListener('load', resolve);
                            img.addEventListener('error', resolve); // 即使出错也继续
                            setTimeout(resolve, 10000); // 10秒超时
                        });
                    });
                    
                    return Promise.all(imagePromises);
                })();
            """)
            
            # 额外等待确保渲染完成
            time.sleep(2)
            
            # 滚动到顶部确保从正确位置开始
            page.evaluate("window.scrollTo(0, 0)")
            time.sleep(1)
            
        except Exception as e:
            print(f"页面渲染优化失败: {e}")
    
    def _convert_to_pdf(self, page, save_path):
        """转换为PDF"""
        try:
            pdf_options = {
                'path': save_path,
                'format': 'A4',
                'print_background': True,
                'margin': {
                    'top': '0.5in',
                    'right': '0.5in',
                    'bottom': '0.5in',
                    'left': '0.5in'
                },
                'prefer_css_page_size': True
            }
            
            print(f"PDF选项: {pdf_options}")
            page.pdf(**pdf_options)
            
        except Exception as e:
            raise RuntimeError(f"PDF生成失败: {str(e)}")
    
    def _capture_image(self, page, save_path, format_type, screenshot_type):
        """智能截图并保存为图片 - 处理大页面"""
        try:
            # 首先尝试直接截图
            if self._try_direct_screenshot(page, save_path, format_type, screenshot_type):
                return
            
            # 如果直接截图失败，使用智能分片截图
            print("⚠ 直接截图失败，使用智能分片截图...")
            self._smart_screenshot_with_tiling(page, save_path, format_type, screenshot_type)
            
        except Exception as e:
            print(f"截图失败，尝试使用备用方案: {e}")
            # 最后的备用方案：降低质量截图
            self._fallback_screenshot(page, save_path, format_type, screenshot_type)
    
    def _try_direct_screenshot(self, page, save_path, format_type, screenshot_type):
        """尝试直接截图"""
        try:
            # 检查页面尺寸
            page_info = page.evaluate("""
                () => {
                    const body = document.body;
                    const html = document.documentElement;
                    return {
                        width: Math.max(body.scrollWidth, html.scrollWidth, body.offsetWidth, html.offsetWidth),
                        height: Math.max(body.scrollHeight, html.scrollHeight, body.offsetHeight, html.offsetHeight),
                        viewportWidth: window.innerWidth,
                        viewportHeight: window.innerHeight
                    };
                }
            """)
            
            print(f"页面尺寸: {page_info['width']}x{page_info['height']}")
            print(f"视口尺寸: {page_info['viewportWidth']}x{page_info['viewportHeight']}")
            
            # 计算估计的图片大小（像素 * 4字节/像素 用于RGBA）
            estimated_size_mb = (page_info['width'] * page_info['height'] * 4) / (1024 * 1024)
            print(f"预估图片大小: {estimated_size_mb:.1f} MB")
            
            # 如果页面过大（超过500MB或尺寸超过32000像素），直接使用分片截图
            if estimated_size_mb > 500 or page_info['width'] > 32000 or page_info['height'] > 32000:
                print("⚠ 页面过大，跳过直接截图")
                return False
            
            # 尝试直接截图
            screenshot_options = {
                'path': save_path,
                'type': 'png' if format_type != 'JPEG' else 'jpeg',
                'full_page': screenshot_type == "full_page"
            }
            
            if format_type == 'JPEG':
                screenshot_options['quality'] = 95
            
            print(f"尝试直接截图，选项: {screenshot_options}")
            page.screenshot(**screenshot_options)
            print("✓ 直接截图成功")
            return True
            
        except Exception as e:
            print(f"直接截图失败: {e}")
            return False
    
    def _smart_screenshot_with_tiling(self, page, save_path, format_type, screenshot_type):
        """智能分片截图并合并"""
        try:
            from PIL import Image
        except ImportError:
            raise RuntimeError("分片截图需要安装PIL/Pillow库: pip install Pillow")
        
        try:
            # 获取页面完整尺寸
            page_info = page.evaluate("""
                () => {
                    const body = document.body;
                    const html = document.documentElement;
                    return {
                        width: Math.max(body.scrollWidth, html.scrollWidth, body.offsetWidth, html.offsetWidth),
                        height: Math.max(body.scrollHeight, html.scrollHeight, body.offsetHeight, html.offsetHeight),
                        viewportWidth: window.innerWidth,
                        viewportHeight: window.innerHeight
                    };
                }
            """)
            
            full_width = page_info['width']
            full_height = page_info['height']
            viewport_width = page_info['viewportWidth']
            viewport_height = page_info['viewportHeight']
            
            print(f"开始分片截图: 完整尺寸 {full_width}x{full_height}")
            
            # 计算合理的分片大小（避免单片过大）
            max_tile_size = 8000  # 每片最大8000像素
            tile_width = min(viewport_width, max_tile_size)
            tile_height = min(viewport_height, max_tile_size)
            
            # 确保分片有重叠以避免拼接问题
            overlap = 50
            
            # 计算需要多少片
            cols = max(1, (full_width + tile_width - overlap - 1) // (tile_width - overlap))
            rows = max(1, (full_height + tile_height - overlap - 1) // (tile_height - overlap))
            
            print(f"分片策略: {cols}列 x {rows}行，每片 {tile_width}x{tile_height}")
            
            # 创建最终图像
            final_image = Image.new('RGB', (full_width, full_height), 'white')
            
            # 逐片截图
            import tempfile
            temp_dir = tempfile.mkdtemp()
            
            for row in range(rows):
                for col in range(cols):
                    # 计算当前片的位置
                    x = col * (tile_width - overlap)
                    y = row * (tile_height - overlap)
                    
                    # 确保不超出边界
                    actual_width = min(tile_width, full_width - x)
                    actual_height = min(tile_height, full_height - y)
                    
                    if actual_width <= 0 or actual_height <= 0:
                        continue
                    
                    print(f"截取片段 ({row+1}/{rows}, {col+1}/{cols}): {x},{y} {actual_width}x{actual_height}")
                    
                    # 滚动到指定位置
                    page.evaluate(f"window.scrollTo({x}, {y})")
                    time.sleep(0.5)  # 等待滚动完成
                    
                    # 截取当前视口
                    temp_path = os.path.join(temp_dir, f"tile_{row}_{col}.png")
                    page.screenshot(path=temp_path, clip={
                        'x': 0,
                        'y': 0,
                        'width': min(actual_width, viewport_width),
                        'height': min(actual_height, viewport_height)
                    })
                    
                    # 拼接到最终图像
                    try:
                        tile_image = Image.open(temp_path)
                        final_image.paste(tile_image, (x, y))
                        tile_image.close()
                        os.remove(temp_path)
                    except Exception as e:
                        print(f"片段拼接警告: {e}")
            
            # 保存最终图像
            if format_type == 'JPEG':
                final_image = final_image.convert('RGB')
                final_image.save(save_path, 'JPEG', quality=95, optimize=True)
            elif format_type == 'WEBP':
                final_image.save(save_path, 'WEBP', quality=95, method=6)
            else:  # PNG
                final_image.save(save_path, 'PNG', optimize=True)
            
            final_image.close()
            
            # 清理临时目录
            try:
                os.rmdir(temp_dir)
            except:
                pass
            
            print(f"✓ 分片截图完成: {save_path}")
            
        except Exception as e:
            raise RuntimeError(f"分片截图失败: {str(e)}")
    
    def _fallback_screenshot(self, page, save_path, format_type, screenshot_type):
        """备用截图方案 - 降低质量但确保成功"""
        try:
            print("使用备用截图方案...")
            
            # 降低页面缩放
            page.evaluate("document.body.style.zoom = '0.5'")
            time.sleep(1)
            
            # 尝试截图当前视口
            screenshot_options = {
                'path': save_path,
                'type': 'png' if format_type != 'JPEG' else 'jpeg',
                'full_page': False  # 强制只截取当前视口
            }
            
            if format_type == 'JPEG':
                screenshot_options['quality'] = 80  # 降低质量
            
            page.screenshot(**screenshot_options)
            print("✓ 备用截图成功（可能只包含部分内容）")
            
        except Exception as e:
            raise RuntimeError(f"所有截图方案都失败了: {str(e)}")
        
        # 如果是WEBP格式，需要转换
        if format_type == 'WEBP':
            self._convert_to_webp(save_path)
    
    def _convert_to_webp(self, image_path):
        """将PNG转换为WEBP"""
        try:
            try:
                from PIL import Image
            except ImportError:
                print("警告: PIL/Pillow未安装，无法转换为WEBP格式")
                return
            
            # 生成WEBP路径
            webp_path = image_path.replace('.png', '.webp')
            
            # 转换
            with Image.open(image_path) as img:
                img.save(webp_path, 'WEBP', quality=95, method=6)
            
            # 删除原PNG文件
            os.remove(image_path)
            
            # 重命名WEBP文件
            if webp_path != image_path:
                os.rename(webp_path, image_path)
                
        except Exception as e:
            print(f"WEBP转换失败: {e}")


# 为了兼容性保持原有的类名
ScreenshotHandler = ScreenshotCapture
