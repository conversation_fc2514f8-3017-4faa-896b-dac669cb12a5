"""
千帆封面生成器 - 配置文件
配置主题色、图标、API设置等
"""

import os

# DeepSeek API配置
DEEPSEEK_CONFIG = {
    "api_key": "***********************************",
    "base_url": "https://api.deepseek.com",
    "model": "deepseek-chat",
    "timeout": 30
}

# 主题色配置 - 工商经管相关
THEME_COLORS = {
    "科技蓝": {
        "primary": "#1e3a8a",
        "secondary": "#3b82f6", 
        "accent": "#fbbf24",
        "text": "#f9fafb",
        "description": "深蓝到科技蓝渐变，金色点缀"
    },
    "商务绿": {
        "primary": "#065f46",
        "secondary": "#10b981",
        "accent": "#f59e0b",
        "text": "#f9fafb",
        "description": "深绿到翠绿渐变，橙色点缀"
    },
    "财经金": {
        "primary": "#92400e",
        "secondary": "#f59e0b",
        "accent": "#1d4ed8",
        "text": "#f9fafb",
        "description": "深棕到金色渐变，蓝色点缀"
    },
    "管理紫": {
        "primary": "#581c87",
        "secondary": "#8b5cf6",
        "accent": "#10b981",
        "text": "#f9fafb",
        "description": "深紫到淡紫渐变，绿色点缀"
    },
    "经济红": {
        "primary": "#991b1b",
        "secondary": "#ef4444",
        "accent": "#fbbf24",
        "text": "#f9fafb",
        "description": "深红到红色渐变，金色点缀"
    },
    "学院蓝": {
        "primary": "#1e40af",
        "secondary": "#60a5fa",
        "accent": "#f97316",
        "text": "#f9fafb",
        "description": "学院蓝到天蓝渐变，橙色点缀"
    }
}

# SVG图标配置 - 工商经管相关
ICON_SETS = {
    "数据分析": {
        "icon": '''<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M7.5 14.25v2.25m3-4.5v4.5m3-6.75v6.75m3-9v9M6 20.25h12A2.25 2.25 0 0 0 20.25 18V6A2.25 2.25 0 0 0 18 3.75H6A2.25 2.25 0 0 0 3.75 6v12A2.25 2.25 0 0 0 6 20.25Z" />
        </svg>''',
        "keywords": ["数据", "分析", "统计", "经济学", "管理科学"]
    },
    "商务管理": {
        "icon": '''<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M20.25 14.15v4.25c0 1.094-.787 2.036-1.872 2.18-2.087.277-4.216.42-6.378.42s-4.291-.143-6.378-.42c-1.085-.144-1.872-1.086-1.872-2.18v-4.25m16.5 0a2.18 2.18 0 0 0 .75-1.661V8.706c0-1.081-.768-2.015-1.837-2.175a48.114 48.114 0 0 0-3.413-.387m4.5 8.006c-.194.165-.42.295-.673.38A23.978 23.978 0 0 1 12 15.75c-2.648 0-5.195-.429-7.577-1.22a2.016 2.016 0 0 1-.673-.38m0 0A2.18 2.18 0 0 1 3 12.489V8.706c0-1.081.768-2.015 1.837-2.175a48.111 48.111 0 0 1 3.413-.387m7.5 0V5.25A2.25 2.25 0 0 0 13.5 3h-3a2.25 2.25 0 0 0-2.25 2.25v.894m7.5 0a48.667 48.667 0 0 0-7.5 0M12 12.75h.008v.008H12v-.008Z" />
        </svg>''',
        "keywords": ["商务", "管理", "商业", "企业", "工商"]
    },
    "金融财务": {
        "icon": '''<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.268-.268-1.268-.732 0-.464.543-.732 1.268-.732.629 0 1.268.268 1.268.732m-3 0c0-.464.543-.732 1.268-.732h.464" />
        </svg>''',
        "keywords": ["金融", "财务", "会计", "财政", "税务", "投资"]
    },
    "科技创新": {
        "icon": '''<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 3v1.5M4.5 8.25H3m18 0h-1.5M4.5 12H3m18 0h-1.5m-16.5 3.75H3m18 0h-1.5M8.25 19.5V21M12 3v1.5m0 15V21m3.75-18v1.5m0 15V21m-9-1.5h10.5a3 3 0 0 0 3-3V7.5a3 3 0 0 0-3-3H6.75a3 3 0 0 0-3 3v9a3 3 0 0 0 3 3Z" />
        </svg>''',
        "keywords": ["科技", "技术", "电子", "信息", "计算机", "创新"]
    },
    "国际贸易": {
        "icon": '''<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 21a9.004 9.004 0 0 0 8.716-6.747M12 21a9.004 9.004 0 0 1-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3m0 18c-2.485 0-4.5-4.03-4.5-9S9.515 3 12 3m0 0a8.997 8.997 0 0 1 7.843 4.582M12 3a8.997 8.997 0 0 0-7.843 4.582m15.686 0A11.953 11.953 0 0 1 12 10.5c-2.998 0-5.74-1.1-7.843-2.918m15.686 0A8.959 8.959 0 0 1 21 12c0 .778-.099 1.533-.284 2.253m0 0A17.919 17.919 0 0 1 12 16.5c-3.162 0-6.133-.815-8.716-2.247m0 0A9.015 9.015 0 0 1 3 12c0-1.605.42-3.113 1.157-4.418" />
        </svg>''',
        "keywords": ["国际", "贸易", "外语", "外国语", "商务", "全球"]
    },
    "教育学术": {
        "icon": '''<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M4.26 10.147a60.438 60.438 0 0 0-.491 6.347A48.62 48.62 0 0 1 12 20.904a48.62 48.62 0 0 1 8.232-4.41 60.46 60.46 0 0 0-.491-6.347m-15.482 0a50.636 50.636 0 0 0-2.658-.813A59.906 59.906 0 0 1 12 3.493a59.903 59.903 0 0 1 10.399 5.84c-.896.248-1.783.52-2.658.814m-15.482 0A50.717 50.717 0 0 1 12 13.489a50.702 50.702 0 0 1 7.74-3.342M6.75 15a.75.75 0 1 0 0-1.5.75.75 0 0 0 0 1.5Zm0 0v-3.675A55.378 55.378 0 0 1 12 8.443m-7.007 11.55A5.981 5.981 0 0 0 6.75 15.75v-1.5" />
        </svg>''',
        "keywords": ["教育", "学院", "大学", "MPA", "MBA", "学术"]
    },
    "法律政策": {
        "icon": '''<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 7.5h1.5m-1.5 3h1.5m-7.5 3h7.5m-7.5 3h7.5m3-9h3.375c.621 0 1.125.504 1.125 1.125V18a2.25 2.25 0 0 1-2.25 2.25M16.5 7.5V18a2.25 2.25 0 0 0 2.25 2.25M16.5 7.5V4.875c0-.621-.504-1.125-1.125-1.125H4.125C3.504 3.75 3 4.254 3 4.875V18a2.25 2.25 0 0 0 2.25 2.25h13.5M6 7.5h3v3H6v-3Z" />
        </svg>''',
        "keywords": ["法律", "政法", "政治", "公共", "行政", "政策"]
    },
    "工程技术": {
        "icon": '''<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M11.42 15.17 17.25 21A2.652 2.652 0 0 0 21 17.25l-5.877-5.877M11.42 15.17l2.496-3.03c.317-.384.74-.626 1.208-.766M11.42 15.17l-4.655 5.653a2.548 2.548 0 1 1-3.586-3.586l6.837-5.63m5.108-.233c.55-.164 1.163-.188 1.743-.14a4.5 4.5 0 0 0 4.486-6.336l-3.276 3.277a3.004 3.004 0 0 1-2.25-2.25l3.276-3.276a4.5 4.5 0 0 0-6.336 4.486c.091 1.076-.071 2.264-.904 2.95l-.102.085m-1.745 1.437L5.909 7.5H4.5L2.25 3.75l1.5-1.5L7.5 4.5v1.409l4.26 4.26m-1.745 1.437 1.745-1.437m6.615 8.206L15.75 15.75M4.867 19.125h.008v.008h-.008v-.008Z" />
        </svg>''',
        "keywords": ["工程", "理工", "技术", "机械", "建筑", "材料"]
    }
}

# 模板配置
TEMPLATE_CONFIG = {
    "output_dir": "output",
    "html_filename": "{school_name}_封面设计.html",
    "png_filename": "{school_name}_封面图.png"
}

# HTML转图片配置
SCREENSHOT_CONFIG = {
    "viewport": {"width": 750, "height": 1000},
    "quality": {"scale": 2},
    "format": "PNG",
    "timeout": 60
}

# 获取项目根目录
def get_project_root():
    return os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

# 获取模板文件路径
def get_template_path():
    return os.path.join(get_project_root(), "templates", "cover_template.html")

# 获取输出目录路径
def get_output_dir():
    output_dir = os.path.join(get_project_root(), TEMPLATE_CONFIG["output_dir"])
    os.makedirs(output_dir, exist_ok=True)
    return output_dir
