# 千帆封面图自动生成工具

一个基于AI的自动化封面生成工具，专为工商经管类学院保研资料封面设计。

## 功能特点

- 🤖 **智能识别**: 使用DeepSeek AI自动识别大学和学院名称
- 🎨 **丰富配色**: 20种专业主题色方案，按色系分类，适配各类学院
- 📊 **智能图标**: 25种精选SVG图标，根据学院类型智能匹配
- 🔄 **颜色优化**: 自动避免同校不同学院使用相同颜色
- 📄 **HTML生成**: 基于模板自动生成专业封面HTML
- 🖼️ **高清转换**: 自动转换为高清PNG图片，支持矢量级质量
- 📋 **批量处理**: 支持Excel批量导入，一键生成多个封面
- 📁 **文件管理**: 标准化文件命名和文件夹组织
- 💾 **记忆功能**: 自动记录学校颜色使用历史，智能推荐

## 安装说明

### 1. 环境要求
- Python 3.8+
- Windows 10/11 (推荐)

### 2. 快速安装
1. 双击运行 `install.bat`
2. 等待安装完成

### 3. 手动安装
```bash
# 安装Python依赖
pip install -r requirements.txt

# 安装playwright浏览器
playwright install chromium
```

## 使用方法

### 启动程序

#### Windows用户
- **推荐**: 双击 `启动程序.bat` (最智能的启动方式)
- 或双击 `run.bat` 启动图形界面
- 或命令行运行: `python start.py`
- 或直接运行: `python main.py`

#### Linux/Mac用户
```bash
# 添加执行权限（首次运行）
chmod +x start.sh

# 启动程序
./start.sh

# 或直接使用Python
python3 start.py
```

### 单个处理
1. 在"单个处理"选项卡中输入学院名称
2. 选择主题色（可选，默认自动选择）
3. 点击"预览效果"查看效果
4. 点击"生成封面"开始生成

### 批量处理
1. 准备Excel文件，第一列为学院名称
2. 在"批量处理"选项卡中选择Excel文件
3. 配置处理选项
4. 点击"开始批量处理"

### Excel文件格式
```
学院名称
西南财经大学管理科学与工程学院
中南财经政法大学经济学院
中南财经政法大学财政税务学院
北京外国语大学国际商学院
...
```

## 主题色方案（20种）

### 蓝色系
| 主题名称 | 适用学院 | 配色特点 |
|---------|---------|---------|
| 科技蓝 | 理工科、计算机、电子信息 | 深蓝渐变，科技感强 |
| 深海蓝 | 海洋科学、水利工程 | 深海蓝渐变，海洋气息 |
| 天空蓝 | 航空航天、气象 | 天空蓝渐变，广阔视野 |

### 绿色系
| 主题名称 | 适用学院 | 配色特点 |
|---------|---------|---------|
| 学术绿 | 文学院、教育、人文社科 | 绿色渐变，学术氛围 |
| 森林绿 | 林学院、环境科学 | 森林绿渐变，自然生态 |
| 翡翠绿 | 生物科学、农学 | 翡翠绿渐变，生机盎然 |

### 红色系
| 主题名称 | 适用学院 | 配色特点 |
|---------|---------|---------|
| 医学红 | 医学院、生命科学 | 红色渐变，医学专业 |
| 玫瑰红 | 护理学院、心理学 | 玫瑰红渐变，温暖关怀 |
| 酒红色 | 历史学院、考古 | 酒红色渐变，历史厚重 |

### 紫色系
| 主题名称 | 适用学院 | 配色特点 |
|---------|---------|---------|
| 法政紫 | 法学院、政治、公共管理 | 紫色渐变，庄重权威 |
| 神秘紫 | 哲学院、宗教学 | 神秘紫渐变，深邃思辨 |
| 薰衣草紫 | 外国语学院、文化传播 | 薰衣草紫渐变，优雅文化 |

### 橙色系
| 主题名称 | 适用学院 | 配色特点 |
|---------|---------|---------|
| 艺术橙 | 艺术学院、设计、传媒 | 橙色渐变，创意活力 |
| 活力橙 | 体育学院、运动科学 | 活力橙渐变，运动激情 |
| 琥珀橙 | 地质学院、材料科学 | 琥珀橙渐变，自然质感 |

### 金色系
| 主题名称 | 适用学院 | 配色特点 |
|---------|---------|---------|
| 商务金 | 商学院、经济管理 | 金色渐变，商务专业 |
| 皇家金 | 金融学院、财经 | 皇家金渐变，财富尊贵 |
| 香槟金 | 会计学院、审计 | 香槟金渐变，精确严谨 |

### 灰色系
| 主题名称 | 适用学院 | 配色特点 |
|---------|---------|---------|
| 工程灰 | 工程学院、建筑、土木 | 灰色渐变，工程稳重 |
| 钢铁灰 | 机械学院、冶金 | 钢铁灰渐变，工业力量 |
| 石墨灰 | 化学学院、材料工程 | 石墨灰渐变，科学严谨 |

### 青色系
| 主题名称 | 适用学院 | 配色特点 |
|---------|---------|---------|
| 青瓷色 | 陶瓷学院、工艺美术 | 青瓷色渐变，传统工艺 |
| 湖水青 | 水产学院、海洋工程 | 湖水青渐变，清澈纯净 |

## 输出文件

每个学院会生成独立的文件夹，包含：
- `{大学}_{学院}_封面设计.html` - HTML源文件
- `{大学}_{学院}_封面图.png` - 高清PNG图片

## 配置说明

### DeepSeek API
- 默认API Key已内置
- 可在"设置"选项卡中修改
- 支持API连接测试

### 图片质量
- 默认2倍缩放，生成高清图片
- 支持自动裁剪白边
- 输出PNG格式，保证质量

## 故障排除

### 常见问题

1. **程序无法启动**
   - 检查Python版本是否3.8+
   - 运行 `install.bat` 重新安装依赖

2. **API连接失败**
   - 检查网络连接
   - 在设置中测试API连接
   - 确认API Key有效

3. **图片生成失败**
   - 检查输出目录权限
   - 确保playwright浏览器已安装
   - 尝试降低图片缩放比例

4. **Excel读取失败**
   - 确保文件格式为.xlsx或.xls
   - 检查第一列是否包含学院名称
   - 确保文件未被其他程序占用

### 日志查看
程序运行时会在界面中显示详细日志，可用于问题诊断。

## 技术架构

```
生成封面代码/
├── main.py                    # 主程序入口
├── requirements.txt           # 依赖包列表
├── config/                    # 配置文件
│   ├── settings.py           # 基本配置
│   ├── themes.py             # 主题色配置
│   └── icons.py              # 图标配置
├── src/                      # 源代码模块
│   ├── api_client.py         # DeepSeek API客户端
│   ├── excel_reader.py       # Excel文件读取
│   ├── html_processor.py     # HTML模板处理
│   ├── image_converter.py    # HTML转图片
│   ├── file_manager.py       # 文件管理
│   └── gui.py                # GUI界面
├── templates/                # HTML模板
├── output/                   # 输出文件夹
└── assets/                   # 资源文件
```

## 版本信息

- **版本**: v1.0.0
- **作者**: AI助手
- **更新日期**: 2025-01-12

## 许可证

本项目仅供学习和个人使用。
