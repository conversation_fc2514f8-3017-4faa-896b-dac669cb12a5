#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
千帆封面图生成工具 - 通用启动脚本
支持跨平台启动，自动检查环境和依赖
"""

import sys
import os
import subprocess
import importlib.util

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("❌ Python版本过低")
        print(f"当前版本: {sys.version}")
        print("需要Python 3.8或更高版本")
        print("请访问 https://www.python.org/ 下载最新版本")
        return False
    
    print(f"✅ Python版本: {sys.version.split()[0]}")
    return True

def check_dependencies():
    """检查必需的依赖包"""
    required_packages = [
        ('tkinter', '内置GUI库'),
        ('pandas', 'Excel处理'),
        ('openpyxl', 'Excel文件支持'),
        ('openai', 'API客户端'),
        ('PIL', 'Pillow图像处理'),
        ('playwright', '网页自动化')
    ]
    
    missing_packages = []
    
    print("检查依赖包...")
    for package_name, description in required_packages:
        try:
            if package_name == 'PIL':
                import PIL
            elif package_name == 'tkinter':
                import tkinter
            else:
                importlib.import_module(package_name)
            print(f"✅ {package_name} - {description}")
        except ImportError:
            print(f"❌ {package_name} - {description} (未安装)")
            missing_packages.append(package_name)
    
    return missing_packages

def install_dependencies(missing_packages):
    """安装缺失的依赖包"""
    if not missing_packages:
        return True
    
    print(f"\n发现 {len(missing_packages)} 个缺失的依赖包")
    print("正在尝试自动安装...")
    
    # 准备安装命令
    pip_packages = []
    for package in missing_packages:
        if package == 'PIL':
            pip_packages.append('Pillow')
        elif package == 'tkinter':
            print("⚠️  tkinter是Python内置库，如果缺失请重新安装Python")
            continue
        else:
            pip_packages.append(package)
    
    if pip_packages:
        try:
            # 尝试安装
            cmd = [sys.executable, '-m', 'pip', 'install'] + pip_packages
            print(f"执行命令: {' '.join(cmd)}")
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ 依赖包安装成功")
                
                # 安装playwright浏览器
                if 'playwright' in pip_packages:
                    print("正在安装playwright浏览器...")
                    browser_cmd = [sys.executable, '-m', 'playwright', 'install', 'chromium']
                    browser_result = subprocess.run(browser_cmd, capture_output=True, text=True)
                    
                    if browser_result.returncode == 0:
                        print("✅ playwright浏览器安装成功")
                    else:
                        print("⚠️  playwright浏览器安装失败，稍后可手动安装")
                        print("手动安装命令: playwright install chromium")
                
                return True
            else:
                print("❌ 依赖包安装失败")
                print("错误信息:", result.stderr)
                return False
                
        except Exception as e:
            print(f"❌ 安装过程中发生错误: {e}")
            return False
    
    return True

def check_main_file():
    """检查主程序文件"""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    main_file = os.path.join(script_dir, 'main.py')
    
    if os.path.exists(main_file):
        print(f"✅ 找到主程序: {main_file}")
        return main_file
    else:
        print(f"❌ 未找到主程序文件: {main_file}")
        return None

def start_application(main_file):
    """启动应用程序"""
    try:
        print("\n🚀 启动千帆封面图生成工具...")
        print("=" * 50)
        
        # 切换到程序目录
        os.chdir(os.path.dirname(main_file))
        
        # 导入并运行主程序
        sys.path.insert(0, os.path.dirname(main_file))
        
        # 动态导入main模块
        spec = importlib.util.spec_from_file_location("main", main_file)
        main_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(main_module)
        
        # 调用main函数
        if hasattr(main_module, 'main'):
            return main_module.main()
        else:
            print("❌ 主程序中未找到main函数")
            return 1
            
    except KeyboardInterrupt:
        print("\n⚠️  用户中断程序")
        return 0
    except Exception as e:
        print(f"❌ 程序启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

def main():
    """主函数"""
    print("=" * 60)
    print("🎨 千帆封面图生成工具 v1.0.0")
    print("=" * 60)
    print()
    
    # 检查Python版本
    if not check_python_version():
        input("按回车键退出...")
        return 1
    
    print()
    
    # 检查依赖
    missing_packages = check_dependencies()
    
    if missing_packages:
        print(f"\n⚠️  发现缺失依赖: {', '.join(missing_packages)}")
        
        # 询问是否自动安装
        try:
            choice = input("\n是否自动安装缺失的依赖包? (y/n): ").lower().strip()
            if choice in ['y', 'yes', '是', '']:
                if not install_dependencies(missing_packages):
                    print("\n❌ 依赖安装失败，请手动安装后重试")
                    print("手动安装命令:")
                    print("pip install -r requirements.txt")
                    print("playwright install chromium")
                    input("按回车键退出...")
                    return 1
                
                # 重新检查依赖
                print("\n重新检查依赖...")
                remaining_missing = check_dependencies()
                if remaining_missing:
                    print(f"\n❌ 仍有依赖缺失: {', '.join(remaining_missing)}")
                    input("按回车键退出...")
                    return 1
            else:
                print("\n请手动安装依赖后重试:")
                print("pip install -r requirements.txt")
                print("playwright install chromium")
                input("按回车键退出...")
                return 1
        except KeyboardInterrupt:
            print("\n⚠️  用户取消")
            return 1
    
    print()
    
    # 检查主程序文件
    main_file = check_main_file()
    if not main_file:
        input("按回车键退出...")
        return 1
    
    print()
    
    # 启动应用
    try:
        return start_application(main_file)
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        input("按回车键退出...")
        return 1

if __name__ == "__main__":
    sys.exit(main())
