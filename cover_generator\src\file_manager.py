"""
文件管理器
负责管理输出文件的组织和保存
"""

import os
import shutil
from typing import Dict
from .config import get_output_dir, TEMPLATE_CONFIG

class FileManager:
    def __init__(self):
        self.output_dir = get_output_dir()
    
    def create_school_folder(self, school_info: Dict) -> str:
        """
        为学校创建输出文件夹
        
        Args:
            school_info: 学校信息字典
        
        Returns:
            str: 文件夹路径
        """
        # 构建文件夹名称
        university = school_info.get("university", "未知大学")
        college = school_info.get("college", "未知学院")
        
        # 清理文件名中的非法字符
        folder_name = self._sanitize_filename(f"{university}_{college}")
        
        # 创建文件夹路径
        school_folder = os.path.join(self.output_dir, folder_name)
        
        # 创建文件夹
        os.makedirs(school_folder, exist_ok=True)
        
        return school_folder
    
    def get_html_path(self, school_folder: str, school_info: Dict) -> str:
        """
        获取HTML文件路径
        """
        university = school_info.get("university", "未知大学")
        college = school_info.get("college", "未知学院")
        
        if college and college != "未知学院":
            filename = f"{university}_{college}_封面设计.html"
        else:
            filename = f"{university}_封面设计.html"
        
        filename = self._sanitize_filename(filename)
        return os.path.join(school_folder, filename)
    
    def get_png_path(self, school_folder: str, school_info: Dict) -> str:
        """
        获取PNG文件路径
        """
        university = school_info.get("university", "未知大学")
        college = school_info.get("college", "未知学院")
        
        if college and college != "未知学院":
            filename = f"{university}_{college}_封面图.png"
        else:
            filename = f"{university}_封面图.png"
        
        filename = self._sanitize_filename(filename)
        return os.path.join(school_folder, filename)
    
    def save_html_file(self, html_content: str, html_path: str) -> bool:
        """
        保存HTML文件
        """
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(html_path), exist_ok=True)
            
            # 写入HTML文件
            with open(html_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            print(f"✓ HTML文件已保存: {html_path}")
            return True
            
        except Exception as e:
            print(f"保存HTML文件失败: {e}")
            return False
    
    def cleanup_temp_files(self, folder_path: str) -> None:
        """
        清理临时文件
        """
        try:
            temp_patterns = ['*.tmp', '*.temp', 'temp_*']
            for pattern in temp_patterns:
                import glob
                temp_files = glob.glob(os.path.join(folder_path, pattern))
                for temp_file in temp_files:
                    try:
                        os.remove(temp_file)
                    except:
                        pass
        except:
            pass
    
    def get_output_summary(self) -> Dict:
        """
        获取输出目录摘要信息
        """
        try:
            if not os.path.exists(self.output_dir):
                return {"folders": 0, "html_files": 0, "png_files": 0}
            
            folders = 0
            html_files = 0
            png_files = 0
            
            for root, dirs, files in os.walk(self.output_dir):
                if root != self.output_dir:  # 不计算根目录
                    folders += 1
                
                for file in files:
                    if file.endswith('.html'):
                        html_files += 1
                    elif file.endswith('.png'):
                        png_files += 1
            
            return {
                "folders": folders,
                "html_files": html_files,
                "png_files": png_files,
                "output_dir": self.output_dir
            }
            
        except Exception as e:
            print(f"获取输出摘要失败: {e}")
            return {"error": str(e)}
    
    def open_output_folder(self) -> bool:
        """
        打开输出文件夹
        """
        try:
            if os.path.exists(self.output_dir):
                os.startfile(self.output_dir)  # Windows
                return True
            else:
                print("输出文件夹不存在")
                return False
        except Exception as e:
            print(f"打开文件夹失败: {e}")
            return False
    
    def _sanitize_filename(self, filename: str) -> str:
        """
        清理文件名中的非法字符
        """
        # Windows文件名非法字符
        illegal_chars = '<>:"/\\|?*'
        
        for char in illegal_chars:
            filename = filename.replace(char, '_')
        
        # 移除连续的下划线
        while '__' in filename:
            filename = filename.replace('__', '_')
        
        # 移除开头和结尾的下划线和空格
        filename = filename.strip('_ ')
        
        # 限制文件名长度
        if len(filename) > 200:
            filename = filename[:200]
        
        return filename

# 创建全局实例
file_manager = FileManager()
