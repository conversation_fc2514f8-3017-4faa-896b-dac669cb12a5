"""
千帆封面生成器 - 主程序入口
一键启动封面生成器应用
"""

import sys
import os
import traceback

# 添加项目路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def check_dependencies():
    """检查必要的依赖包"""
    required_packages = [
        ('openai', 'openai'),
        ('pandas', 'pandas'), 
        ('playwright', 'playwright'),
        ('tkinter', '内置模块')
    ]
    
    missing_packages = []
    
    for package_name, install_name in required_packages:
        try:
            if package_name == 'tkinter':
                import tkinter
            else:
                __import__(package_name)
            print(f"✓ {package_name} 已安装")
        except ImportError:
            missing_packages.append(install_name)
            print(f"✗ {package_name} 未安装")
    
    if missing_packages:
        print("\n缺少必要依赖包，请先安装:")
        for pkg in missing_packages:
            if pkg != '内置模块':
                print(f"pip install {pkg}")
        
        # 特殊提示
        if 'playwright' in [p[0] for p in required_packages if p[1] in missing_packages]:
            print("\nPlaywright安装后还需要安装浏览器:")
            print("playwright install chromium")
        
        input("\n按回车键退出...")
        return False
    
    print("✓ 依赖包检查通过")
    return True

def main():
    """主函数"""
    print("=" * 60)
    print("✨ 千帆封面生成器 v1.0")
    print("=" * 60)
    
    # 检查依赖
    if not check_dependencies():
        return 1
    
    try:
        # 导入并创建应用
        from src import create_application
        
        print("\n正在启动GUI界面...")
        app = create_application()
        
        print("✓ GUI界面已启动")
        print("💡 使用说明:")
        print("   1. 首先测试API连接")
        print("   2. 单个生成：直接输入学校学院名称")
        print("   3. 批量生成：导入Excel文件（第一列为学校学院名称）")
        print("   4. 生成的文件保存在output文件夹中")
        print("\n程序正在运行，请在GUI窗口中操作...")
        
        # 运行应用
        app.run()
        
        print("\n程序已退出")
        return 0
        
    except ImportError as e:
        print(f"\n导入模块失败: {e}")
        print("请确保所有依赖包已正确安装")
        traceback.print_exc()
        input("\n按回车键退出...")
        return 1
        
    except Exception as e:
        print(f"\n程序运行出错: {e}")
        traceback.print_exc()
        input("\n按回车键退出...")
        return 1

if __name__ == "__main__":
    sys.exit(main())
