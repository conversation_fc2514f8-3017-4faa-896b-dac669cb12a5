#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强功能演示脚本
展示新增的颜色库、图标库和智能选择功能
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from config.themes import get_all_theme_names, get_themes_by_category
from config.icons import get_smart_icons, get_all_categories
from src.color_database import ColorDatabase


def demo_color_intelligence():
    """演示颜色智能选择功能"""
    print("🎨 颜色智能选择演示")
    print("=" * 50)
    
    db = ColorDatabase()
    
    # 模拟同一学校的多个学院
    university = "示例大学"
    colleges = [
        ("计算机学院", "科技蓝"),
        ("软件学院", "科技蓝"),      # 会被替换为深海蓝
        ("信息学院", "深海蓝"),      # 会被替换为天空蓝
        ("商学院", "商务金"),
        ("经济学院", "商务金"),      # 会被替换为皇家金
        ("管理学院", "皇家金"),      # 会被替换为香槟金
    ]
    
    print(f"为 {university} 的各学院分配颜色：\n")
    
    for college, suggested_color in colleges:
        recommended = db.get_recommended_color(university, college, suggested_color)
        db.record_color_usage(university, college, recommended)
        
        status = "✓ 直接使用" if recommended == suggested_color else "🔄 智能替换"
        print(f"{college:12} | AI建议: {suggested_color:8} | 实际使用: {recommended:8} | {status}")
    
    print(f"\n📊 {university} 颜色使用统计:")
    used_colors = db.get_used_colors(university)
    for i, color in enumerate(used_colors, 1):
        print(f"  {i}. {color}")


def demo_smart_icons():
    """演示智能图标选择功能"""
    print("\n\n📊 智能图标选择演示")
    print("=" * 50)
    
    # 不同类型学院的图标选择
    college_types = [
        "商学院",
        "计算机学院", 
        "法学院",
        "医学院",
        "艺术学院",
        "工程学院"
    ]
    
    for college_type in college_types:
        print(f"\n{college_type} 智能选择的图标:")
        icons = get_smart_icons(college_type, 4)
        
        for i, icon in enumerate(icons, 1):
            print(f"  {i}. {icon['name']} ({icon['category']}) - {icon['description']}")


def demo_color_categories():
    """演示颜色分类功能"""
    print("\n\n🌈 颜色分类演示")
    print("=" * 50)
    
    categories = ['蓝色系', '绿色系', '红色系', '紫色系', '橙色系', '金色系', '灰色系', '青色系']
    
    for category in categories:
        themes = get_themes_by_category(category)
        print(f"\n{category}: {len(themes)} 种颜色")
        for theme in themes:
            print(f"  • {theme}")


def demo_icon_categories():
    """演示图标分类功能"""
    print("\n\n🔧 图标分类演示")
    print("=" * 50)
    
    from config.icons import get_icons_by_category
    
    categories = get_all_categories()
    
    for category in categories:
        icons = get_icons_by_category(category)
        print(f"\n{category}: {len(icons)} 个图标")
        for icon in icons[:3]:  # 只显示前3个
            print(f"  • {icon}")
        if len(icons) > 3:
            print(f"  ... 还有 {len(icons) - 3} 个")


def demo_complete_workflow():
    """演示完整工作流程"""
    print("\n\n🚀 完整工作流程演示")
    print("=" * 50)
    
    from src.api_client import DeepSeekClient
    
    client = DeepSeekClient()
    
    # 模拟处理多个学院
    test_colleges = [
        "北京理工大学计算机学院",
        "北京理工大学管理与经济学院", 
        "北京理工大学机械与车辆学院",
        "清华大学计算机科学与技术系",
        "清华大学经济管理学院"
    ]
    
    print("处理学院列表:")
    for i, college in enumerate(test_colleges, 1):
        print(f"  {i}. {college}")
    
    print("\n处理结果:")
    for college_name in test_colleges:
        # 使用备用分析避免API调用
        result = client._fallback_analysis(college_name)
        
        print(f"\n📍 {college_name}")
        print(f"   大学: {result['university']}")
        print(f"   学院: {result['college']}")
        print(f"   主题: {result['theme_color']}")
        
        # 显示智能选择的图标
        icons = get_smart_icons(result['college'], 2)
        icon_names = [icon['name'] for icon in icons]
        print(f"   图标: {', '.join(icon_names)}")


def main():
    """主演示函数"""
    print("🎉 千帆封面图生成工具 - 增强功能演示")
    print("=" * 60)
    
    print("\n新增功能亮点:")
    print("✨ 主题色从 7 种扩展到 23 种")
    print("✨ 图标从 10 个扩展到 26 个") 
    print("✨ 智能避免同校颜色重复")
    print("✨ 根据学院类型智能选择图标")
    print("✨ 颜色按色系分类管理")
    print("✨ 图标按功能分类管理")
    
    try:
        demo_color_intelligence()
        demo_smart_icons()
        demo_color_categories()
        demo_icon_categories()
        demo_complete_workflow()
        
        print("\n" + "=" * 60)
        print("🎊 演示完成！")
        print("=" * 60)
        
        print("\n💡 使用建议:")
        print("• 让AI自动选择主题色，系统会智能避免重复")
        print("• 图标会根据学院类型自动匹配，无需手动选择")
        print("• 同一学校的不同学院会使用不同颜色或同色系变化")
        print("• 所有颜色使用记录会自动保存，支持历史查询")
        
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
