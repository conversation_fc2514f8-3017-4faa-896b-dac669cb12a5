@echo off
chcp 65001 >nul

REM 设置窗口标题
title 千帆封面图生成工具

echo.
echo ████████████████████████████████████████████████████████
echo █                                                      █
echo █            千帆封面图生成工具 v1.0.0                 █
echo █                                                      █
echo ████████████████████████████████████████████████████████
echo.

REM 获取脚本所在目录
set SCRIPT_DIR=%~dp0

REM 切换到脚本目录
cd /d "%SCRIPT_DIR%"

echo 📍 当前目录: %CD%
echo.

REM 检查Python环境
echo 🔍 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到Python
    echo.
    echo 请确保已安装Python 3.8或更高版本
    echo 下载地址: https://www.python.org/
    echo 安装时请勾选 "Add Python to PATH" 选项
    echo.
    goto :error_exit
)

REM 显示Python版本
for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo ✅ Python版本: %PYTHON_VERSION%

REM 检查主程序文件
echo.
echo 🔍 检查程序文件...
if not exist "main.py" (
    echo ❌ 未找到 main.py 文件
    echo.
    echo 请确保在正确的目录中运行此脚本
    echo 当前目录应包含以下文件:
    echo   - main.py
    echo   - requirements.txt
    echo   - config/ 文件夹
    echo   - src/ 文件夹
    echo.
    goto :error_exit
)
echo ✅ 找到主程序文件

REM 检查配置文件夹
if not exist "config" (
    echo ❌ 未找到 config 文件夹
    goto :error_exit
)
if not exist "src" (
    echo ❌ 未找到 src 文件夹
    goto :error_exit
)
echo ✅ 程序结构完整

REM 尝试启动程序
echo.
echo 🚀 启动程序...
echo ================================================
echo.

REM 使用Python启动脚本启动程序
python start.py
set EXIT_CODE=%ERRORLEVEL%

echo.
echo ================================================
if %EXIT_CODE% equ 0 (
    echo ✅ 程序正常退出
) else (
    echo ⚠️  程序异常退出 (错误代码: %EXIT_CODE%)
)

echo.
echo 感谢使用千帆封面图生成工具！
pause
exit /b %EXIT_CODE%

:error_exit
echo.
echo ❌ 启动失败
echo.
echo 💡 解决方案:
echo 1. 确保已安装Python 3.8+
echo 2. 运行 install.bat 安装依赖
echo 3. 检查文件完整性
echo.
echo 如需帮助，请查看 README.md 或 使用指南.md
echo.
pause
exit /b 1
