<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产品封面生成器 - 南开大学 旅游与服务学院</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700;900&display=swap');
        
        /* --- 全局样式和主题配色 --- */
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f8fafc;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            padding: 20px;
            margin: 0;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        #cover-container {
            width: 750px;
            height: 1000px;
            /* 背景色：主题渐变 */
            background: linear-gradient(145deg, #ea580c 0%, #f97316 100%);
            color: #FFFFFF;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            padding: 50px;
            box-sizing: border-box;
            border-radius: 8px;
            overflow: hidden;
            position: relative;
            text-align: center;
            box-shadow: 0 15px 30px rgba(0,0,0,0.15);
        }

        /* --- 装饰性元素 --- */
        #cover-container::before {
            content: '';
            position: absolute;
            top: -150px;
            left: -200px;
            width: 600px;
            height: 600px;
            background: radial-gradient(circle, rgba(251, 146, 60, 0.12) 0%, rgba(251, 191, 36, 0) 70%);
            transform: rotate(45deg);
        }
        #cover-container::after {
            content: '';
            position: absolute;
            bottom: -200px;
            right: -200px;
            width: 550px;
            height: 550px;
            background: radial-gradient(circle, rgba(251, 146, 60, 0.12) 0%, rgba(251, 191, 36, 0) 70%);
            border-radius: 50%;
        }

        .main-focus {
            z-index: 1;
            margin-top: 40px;
        }
        
        /* --- 学院名称样式 --- */
        .main-focus .school-name {
            font-size: 28px;
            font-weight: 500;
            background-color: rgba(0, 0, 0, 0.2);
            color: #fb923c;
            padding: 12px 35px;
            border-radius: 50px;
            display: inline-block;
            margin-bottom: 30px;
            border: 1px solid rgba(251, 146, 60, 0.3);
        }

        .main-focus .main-title {
            font-weight: 900;
            text-shadow: none;
            color: #FFFFFF;
            margin: 0;
            line-height: 1.2;
        }
        
        /* --- 标题样式 --- */
        .main-title span {
            display: block;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        .main-title .line-1 {
            font-size: 60px;  
            font-weight: 700;
            color: #ffedd5;
        }
        .main-title .line-2 {
            font-size: 80px;
            color: #FFFFFF;
            margin-top: 10px;
            letter-spacing: 2px;
        }

        .subtitle {
            font-size: 24px;
            font-weight: 400;
            color: rgba(249, 250, 251, 0.8);
            margin-top: 25px;
            z-index: 1;
        }

        .features-grid {
            margin-top: 40px;  
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 25px;
            z-index: 1;
        }

        /* --- 特点项目样式 --- */
        .feature-item {
            background-color: rgba(0,0,0,0.2);
            border-radius: 8px;
            padding: 25px;
            box-sizing: border-box;
            border-top: 3px solid rgba(251, 146, 60, 0.6);
            text-align: left;
            transition: transform 0.2s ease-in-out, background-color 0.2s ease-in-out;
        }
        
        .feature-item:hover {
            transform: translateY(-5px);
            background-color: rgba(0,0,0,0.3);
        }

        /* --- SVG图标样式 --- */
        .feature-item .icon {
            color: #fb923c;
            margin-bottom: 15px;
            display: block;
        }
        .feature-item .icon svg {
            width: 38px;
            height: 38px;
        }

        .feature-item h3 {
            font-size: 20px;
            font-weight: 700;
            margin: 0 0 10px 0;
            color: #fff;
        }

        .feature-item p {
            font-size: 15px;
            font-weight: 400;
            color: rgba(249, 250, 251, 0.85);
            margin: 0;
            line-height: 1.6;
        }
        
        .footer {
            text-align: center;
            font-size: 16px;
            font-weight: 500;
            color: rgba(249, 250, 251, 0.65);
            z-index: 1;
            padding-bottom: 20px;
        }

    </style>
</head>
<body>

    <div id="cover-container">
        <div class="main-focus">
            <div class="school-name">南开大学 · 旅游与服务学院</div>
            <h1 class="main-title">
                <span class="line-1">保研夏令营 + 预推免</span>
                <span class="line-2">核心资料合集</span>
            </h1>
        </div>
        
        <div class="subtitle">
            精准定位 | 高效备战 | 全面领航
        </div>
        
        <div class="features-grid">
            <div class="feature-item">
                <div class="icon">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M13.5 21v-7.5a.75.75 0 0 1 .75-.75h3a.75.75 0 0 1 .75.75V21m-4.5 0H2.36m11.14 0H18m0 0h3.64m-1.39 0V9.349M3.75 21V9.349m0 0a3.001 3.001 0 0 0 3.75-.615A2.993 2.993 0 0 0 9.75 9.75c.896 0 1.7-.393 2.25-1.016a2.993 2.993 0 0 0 2.25 1.016c.896 0 1.7-.393 2.25-1.016a3.001 3.001 0 0 0 3.75.614m-16.5 0a3.004 3.004 0 0 1-.621-4.72L4.318 3.44A1.5 1.5 0 0 1 5.378 3h13.243a1.5 1.5 0 0 1 1.06.44l1.19 1.189a3 3 0 0 1-.621 4.72M6.75 18h3.75a.75.75 0 0 0 .75-.75V13.5a.75.75 0 0 0-.75-.75H6.75a.75.75 0 0 0-.75.75v3.75c0 .414.336.75.75.75Z" />
        </svg>
                </div>
                <h3>3万字真题及解析</h3>
                <p>全面覆盖经济学原理、管理学基础、数据分析等核心课程，深入分析近年面试真题与考核要点，提供专业解题思路与应对策略。</p>
            </div>
            <div class="feature-item">
                <div class="icon">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M10.5 6a7.5 7.5 0 1 0 7.5 7.5h-7.5V6Z" />
            <path stroke-linecap="round" stroke-linejoin="round" d="M13.5 10.5H21A7.5 7.5 0 0 0 13.5 3v7.5Z" />
        </svg>
                </div>
                <h3>2025最新导师评价信息</h3>
                <p>整合全网真实学生评价与经管学院官方信息，深度分析导师研究方向、指导风格与招生偏好，助你精准匹配科技管理领域导师。</p>
            </div>
            <div class="feature-item">
                <div class="icon">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 3v11.25A2.25 2.25 0 0 0 6 16.5h2.25M3.75 3h-1.5m1.5 0h16.5m0 0h1.5m-1.5 0v11.25A2.25 2.25 0 0 1 18 16.5h-2.25m-7.5 0h7.5m-7.5 0-1 3m8.5-3 1 3m0 0-1-3m1 3-1-3m-9-1.5h.008v.008H8.25v-.008Zm2.25 0h.008v.008H10.5v-.008Zm2.25 0h.008v.008H12.75v-.008Zm2.25 0h.008v.008H15v-.008Zm2.25 0h.008v.008H17.25v-.008Z" />
        </svg>
                </div>
                <h3>保研文书材料大合集</h3>
                <p>提供完整个人陈述、研究计划、简历模板与写作指南，附带高分示例与专家点评，助力文书脱颖而出。</p>
            </div>
            <div class="feature-item">
                <div class="icon">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M20.25 14.15v4.25c0 1.094-.787 2.036-1.872 2.18-2.087.277-4.216.42-6.378.42s-4.291-.143-6.378-.42c-1.085-.144-1.872-1.086-1.872-2.18v-4.25m16.5 0a2.18 2.18 0 0 0 .75-1.661V8.706c0-1.081-.768-2.015-1.837-2.175a48.114 48.114 0 0 0-3.413-.387m4.5 8.006c-.194.165-.42.295-.673.38A23.978 23.978 0 0 1 12 15.75c-2.648 0-5.195-.429-7.577-1.22a2.016 2.016 0 0 1-.673-.38m0 0A2.18 2.18 0 0 1 3 12.489V8.706c0-1.081.768-2.015 1.837-2.175a48.111 48.111 0 0 1 3.413-.387m7.5 0V5.25A2.25 2.25 0 0 0 13.5 3h-3a2.25 2.25 0 0 0-2.25 2.25v.894m7.5 0a48.667 48.667 0 0 0-7.5 0M12 12.75h.008v.008H12v-.008Z" />
        </svg>
                </div>
                <h3>核心考点配套复习资料</h3>
                <p>精炼经济学基础、信息管理、电子商务等核心考点，整合配套复习资料与习题详解，构建科技管理系统知识体系。</p>
            </div>
        </div>

        <div class="footer">
            保研喵学姐团队 倾心整理 | 2025版
        </div>
    </div>

</body>
</html>
