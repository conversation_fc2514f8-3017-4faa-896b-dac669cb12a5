@echo off
setlocal enabledelayedexpansion
chcp 65001 > nul
title HTML转图片工具 - 打包脚本

echo ========================================
echo HTML转图片工具 - EXE打包脚本
echo 版本: v2.0.3
echo ========================================
echo.

:: 设置变量
set PROJECT_NAME=HTML转图片工具
set MAIN_FILE=main.py
set ICON_FILE=icon.ico
set DIST_DIR=dist
set BUILD_DIR=build
set SPEC_FILE=main.spec

:: 检查Python环境
echo [1/6] 检查Python环境...
python --version > nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python环境，请先安装Python
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)
echo ✅ Python环境检查通过

:: 检查主文件
echo.
echo [2/6] 检查项目文件...
if not exist %MAIN_FILE% (
    echo ❌ 错误: 未找到主文件 %MAIN_FILE%
    echo 请确保在项目根目录运行此脚本
    pause
    exit /b 1
)
echo ✅ 项目文件检查通过

:: 安装PyInstaller
echo.
echo [3/6] 安装打包工具...
echo 正在安装/更新 PyInstaller...
pip install pyinstaller --upgrade --quiet
if errorlevel 1 (
    echo ❌ 错误: PyInstaller安装失败
    echo 请检查网络连接或手动安装: pip install pyinstaller
    pause
    exit /b 1
)
echo ✅ PyInstaller 安装成功

:: 创建图标文件
echo.
echo [4/6] 创建应用图标...

:: 首先尝试运行专用的图标生成脚本
if exist "create_icon.py" (
    echo 正在使用专用图标生成器...
    python create_icon.py
) else (
    echo 使用内置图标生成器...
    python -c "
import sys
import os

def create_fallback_icon():
    try:
        from PIL import Image, ImageDraw
        print('正在创建应用图标...')
        
        # 创建多个尺寸的图标
        sizes = [(16, 16), (32, 32), (48, 48), (64, 64)]
        images = []
        
        for size in sizes:
            # 创建透明背景
            img = Image.new('RGBA', size, (0, 0, 0, 0))
            draw = ImageDraw.Draw(img)
            
            # 计算比例
            scale = size[0] / 64.0
            
            # 绘制文档背景 (浅橙色)
            margin = max(1, int(2 * scale))
            doc_rect = [margin, margin, size[0] - margin, size[1] - margin]
            draw.rectangle(doc_rect, fill=(247, 187, 131, 255))
            
            # 绘制文档边框 (深棕色)
            draw.rectangle(doc_rect, outline=(110, 65, 35, 255), width=max(1, int(1 * scale)))
            
            # 绘制HTML标志
            if size[0] >= 16:
                center_x = size[0] // 2
                center_y = size[1] // 2
                
                # 绘制 < 符号
                left_x = int(center_x - 8 * scale)
                draw.text((left_x, int(center_y - 4 * scale)), '<', fill=(110, 65, 35, 255))
                
                # 绘制 > 符号  
                right_x = int(center_x + 4 * scale)
                draw.text((right_x, int(center_y - 4 * scale)), '>', fill=(110, 65, 35, 255))
            
            images.append(img)
        
        # 保存为ICO文件
        images[0].save('icon.ico', format='ICO', append_images=images[1:], sizes=sizes)
        print('✅ 图标创建成功')
        return True
        
    except ImportError:
        print('⚠️  PIL未安装，将使用默认图标')
        return False
    except Exception as e:
        print(f'⚠️  图标创建失败: {e}，将使用默认图标')
        return False

# 执行图标创建
create_fallback_icon()
"
)

if not exist %ICON_FILE% (
    echo ⚠️  图标文件创建失败，将使用默认图标
)

:: 清理旧的构建文件
echo.
echo [5/6] 清理旧文件...
if exist %DIST_DIR% (
    echo 删除旧的 %DIST_DIR% 目录...
    rmdir /s /q %DIST_DIR%
)
if exist %BUILD_DIR% (
    echo 删除旧的 %BUILD_DIR% 目录...
    rmdir /s /q %BUILD_DIR%
)
if exist %SPEC_FILE% (
    echo 删除旧的 %SPEC_FILE% 文件...
    del %SPEC_FILE%
)
echo ✅ 清理完成

:: 开始打包
echo.
echo [6/6] 开始打包应用...
echo 这可能需要几分钟时间，请耐心等待...
echo.

:: 询问打包方式
echo 请选择打包方式:
echo 1. 单文件打包 (推荐) - 生成一个独立的exe文件
echo 2. 目录打包 - 生成包含多个文件的目录
echo 3. 使用自定义配置文件打包
echo.
set /p PACK_MODE="请输入选择 (1/2/3): "

if "%PACK_MODE%"=="1" (
    echo 选择: 单文件打包模式
    goto SINGLE_FILE
) else if "%PACK_MODE%"=="2" (
    echo 选择: 目录打包模式
    goto DIRECTORY
) else if "%PACK_MODE%"=="3" (
    echo 选择: 自定义配置文件打包
    goto CUSTOM_SPEC
) else (
    echo 无效选择，使用默认单文件打包模式
    goto SINGLE_FILE
)

:SINGLE_FILE
:: 单文件打包
set PYINSTALLER_CMD=pyinstaller --onefile --windowed --name "%PROJECT_NAME%"

:: 添加图标
if exist %ICON_FILE% (
    set PYINSTALLER_CMD=%PYINSTALLER_CMD% --icon=%ICON_FILE%
)

:: 添加数据文件
if exist "src" (
    set PYINSTALLER_CMD=%PYINSTALLER_CMD% --add-data "src;src"
)

:: 添加隐藏导入
set PYINSTALLER_CMD=%PYINSTALLER_CMD% --hidden-import=tkinter --hidden-import=PIL --hidden-import=numpy --hidden-import=cv2 --hidden-import=playwright --hidden-import=fitz --hidden-import=PyPDF2

:: 优化选项
set PYINSTALLER_CMD=%PYINSTALLER_CMD% --optimize=2 --strip

:: 添加主文件
set PYINSTALLER_CMD=%PYINSTALLER_CMD% %MAIN_FILE%
goto EXECUTE

:DIRECTORY
:: 目录打包
set PYINSTALLER_CMD=pyinstaller --onedir --windowed --name "%PROJECT_NAME%"

:: 添加图标
if exist %ICON_FILE% (
    set PYINSTALLER_CMD=%PYINSTALLER_CMD% --icon=%ICON_FILE%
)

:: 添加数据文件
if exist "src" (
    set PYINSTALLER_CMD=%PYINSTALLER_CMD% --add-data "src;src"
)

:: 添加隐藏导入
set PYINSTALLER_CMD=%PYINSTALLER_CMD% --hidden-import=tkinter --hidden-import=PIL --hidden-import=numpy --hidden-import=cv2 --hidden-import=playwright --hidden-import=fitz --hidden-import=PyPDF2

:: 添加主文件
set PYINSTALLER_CMD=%PYINSTALLER_CMD% %MAIN_FILE%
goto EXECUTE

:CUSTOM_SPEC
:: 使用配置文件打包
if exist "pyinstaller_config.spec" (
    echo 使用自定义配置文件: pyinstaller_config.spec
    set PYINSTALLER_CMD=pyinstaller pyinstaller_config.spec
) else (
    echo ❌ 错误: 未找到配置文件 pyinstaller_config.spec
    echo 将使用默认单文件打包模式
    goto SINGLE_FILE
)

:EXECUTE
echo 执行命令: %PYINSTALLER_CMD%
echo.

%PYINSTALLER_CMD%

:: 检查打包结果
if errorlevel 1 (
    echo.
    echo ❌ 打包失败！
    echo 请检查错误信息并修复问题后重试
    echo.
    echo 常见问题解决方案:
    echo 1. 确保所有依赖包已正确安装: pip install -r requirements.txt
    echo 2. 检查代码中是否有语法错误
    echo 3. 确保没有缺失的模块或文件
    echo 4. 尝试重新安装PyInstaller: pip install --upgrade pyinstaller
    echo 5. 检查Python版本兼容性 (建议使用Python 3.8-3.11)
    echo.
    pause
    exit /b 1
)

:: 检查生成的exe文件
if "%PACK_MODE%"=="2" (
    set "EXE_FILE=%DIST_DIR%\%PROJECT_NAME%\%PROJECT_NAME%.exe"
) else (
    set "EXE_FILE=%DIST_DIR%\%PROJECT_NAME%.exe"
)

if exist "%EXE_FILE%" (
    echo.
    echo ========================================
    echo ✅ 打包成功！
    echo ========================================
    echo.
    echo 📁 输出目录: %CD%\%DIST_DIR%
    echo 📄 可执行文件: %PROJECT_NAME%.exe
    echo 📦 文件大小: 
    for %%A in ("%EXE_FILE%") do echo    %%~zA 字节 (约 %%~zA:~0,-6%MB)
    
    :: 显示构建信息
    echo.
    echo 📋 构建信息:
    echo    Python版本: 
    python --version | findstr /C:"Python"
    echo    PyInstaller版本:
    pyinstaller --version 2>nul || echo    未知
    echo    构建时间: %date% %time%
      if "%PACK_MODE%"=="2" (
        echo    打包模式: 目录分发
        echo    📁 分发目录: %DIST_DIR%\%PROJECT_NAME%\
    ) else (
        echo    打包模式: 单文件
    )
    
    echo.
    echo 🚀 您可以将exe文件分发给其他用户
    echo 💡 建议在不同的电脑上测试exe文件是否正常运行
    echo.
      :: 询问是否立即测试
    set /p TEST_CHOICE="是否立即测试exe文件？(Y/N): "
    if /i "!TEST_CHOICE!"=="Y" (
        echo 正在启动应用进行测试...
        echo 请检查应用是否正常启动和运行
        start "" "!EXE_FILE!"
        echo.
        pause
    )
    
    :: 询问是否打开输出目录
    set /p OPEN_CHOICE="是否打开输出目录？(Y/N): "
    if /i "!OPEN_CHOICE!"=="Y" (
        if "%PACK_MODE%"=="2" (
            explorer "%CD%\%DIST_DIR%\%PROJECT_NAME%"
        ) else (
            explorer "%CD%\%DIST_DIR%"
        )
    )
    
) else (
    echo.
    echo ❌ 错误: 未找到生成的exe文件
    echo 预期位置: %EXE_FILE%
    echo 请检查打包过程中的错误信息
    echo.
)

:: 清理中间文件 (可选)
set /p CLEAN_CHOICE="是否清理中间文件？(删除build目录和spec文件) (Y/N): "
if /i "!CLEAN_CHOICE!"=="Y" (
    if exist %BUILD_DIR% (
        rmdir /s /q %BUILD_DIR%
        echo ✅ 已删除 %BUILD_DIR% 目录
    )
    if exist %SPEC_FILE% (
        del %SPEC_FILE%
        echo ✅ 已删除 %SPEC_FILE% 文件
    )
    if exist "pyinstaller_config.spec" (
        :: 不删除自定义配置文件
        echo ℹ️  保留自定义配置文件 pyinstaller_config.spec
    )
)

echo.
echo ========================================
echo 打包脚本执行完成
echo ========================================
pause
