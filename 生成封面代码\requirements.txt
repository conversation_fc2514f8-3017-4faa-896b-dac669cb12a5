# 千帆封面图生成工具依赖包列表

# 核心依赖
playwright>=1.40.0          # 网页自动化和截图
Pillow>=10.0.0             # 图像处理
pandas>=2.0.0              # Excel文件处理
openpyxl>=3.1.0            # Excel文件读写
openai>=1.0.0              # OpenAI兼容API客户端（用于DeepSeek）

# 可选依赖（用于图像处理优化）
numpy>=1.24.0              # 数值计算
opencv-python>=4.8.0       # 图像处理（可选）

# 安装说明：
# 1. 安装Python包：pip install -r requirements.txt
# 2. 安装playwright浏览器：playwright install chromium
# 3. 如果遇到权限问题，可以使用：pip install -r requirements.txt --user
