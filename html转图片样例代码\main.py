"""
HTML转图片工具 - 简约版
主程序入口

功能特点：
1. 简约朴素的界面设计
2. 导入HTML文件
3. 高清截图生成，支持进度显示
4. 智能裁剪白边
5. 下拉框选择格式和清晰度
6. PDF单页矢量输出，文字可选择
7. 模块化架构，易于维护
8. 转换完成自动打开文件夹功能
9. 详细错误提示和解决方案
10. 进度条保持显示，可手动隐藏

作者：AI助手
版本：2.0.3 - PDF白边裁剪版
更新：修复进度条消失、转换失败提示、新增打开文件夹功能、PDF白边自动裁剪
"""

import sys
import os
import traceback
from tkinter import messagebox

# 添加模块路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from src import HTMLToImageGUI, ScreenshotHandler, DEFAULT_CONFIG, GUI_TYPE
    print(f"使用界面: {GUI_TYPE}")
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保所有依赖包已正确安装")
    sys.exit(1)


class HTMLToImageApp:
    """HTML转图片应用主类"""
    
    def __init__(self):
        self.config = DEFAULT_CONFIG
        self.screenshot_handler = None
        self.gui = None
        
    def initialize(self):
        """初始化应用"""
        try:
            # 初始化截图处理器
            self.screenshot_handler = ScreenshotHandler(self.config)
            
            # 初始化GUI
            self.gui = HTMLToImageGUI(self.screenshot_handler)
            
            print(f"✨ HTML转图片工具 {GUI_TYPE} 已启动")
            return True
            
        except Exception as e:
            error_msg = f"应用初始化失败: {str(e)}\n\n详细错误:\n{traceback.format_exc()}"
            print(error_msg)
            try:
                messagebox.showerror("初始化错误", error_msg)
            except:
                pass
            return False
    
    def run(self):
        """运行应用"""
        if not self.initialize():
            return False
            
        try:
            print("启动HTML转图片工具...")
            print(f"配置: {self.config['viewport']}")
            print("程序已启动，等待用户操作...")
            
            # 运行tkinter界面
            self.gui.run()
            print("程序已退出")
            return True
            
        except KeyboardInterrupt:
            print("\n用户中断程序")
            return True
            
        except Exception as e:
            error_msg = f"程序运行时发生错误: {str(e)}\n\n详细错误:\n{traceback.format_exc()}"
            print(error_msg)
            try:
                messagebox.showerror("运行时错误", error_msg)
            except:
                pass
            return False


def check_dependencies():
    """检查依赖包"""
    required_packages = [
        ('playwright', 'playwright'),
        ('PIL', 'Pillow'),
        ('numpy', 'numpy'),
        ('cv2', 'opencv-python'),
        ('fitz', 'pymupdf'),
        ('PyPDF2', 'PyPDF2')
    ]
    
    missing_packages = []
    
    for package_name, pip_name in required_packages:
        try:
            __import__(package_name)
            print(f"✓ {package_name} 已安装")
        except ImportError:
            missing_packages.append(pip_name)
            print(f"✗ {package_name} 未安装")
    
    if missing_packages:
        error_msg = f"""
缺少以下必需依赖包，请先安装：

{chr(10).join(f'pip install {pkg}' for pkg in missing_packages)}

如果是首次运行，建议运行 install_dependencies.bat 自动安装所有依赖。

或者运行以下命令一次性安装所有依赖：
pip install {' '.join(missing_packages)}
"""
        print(error_msg)
        try:
            messagebox.showerror("依赖包缺失", error_msg)
        except:
            pass
        return False
    
    print("✓ 核心依赖包检查通过")
    return True


def main():
    """主函数"""
    print("=" * 60)
    print("✨ HTML转图片工具 - PDF白边裁剪版 v2.0.3")
    print("=" * 60)
    
    # 检查依赖
    if not check_dependencies():
        input("按回车键退出...")
        return 1
    
    try:
        # 创建并运行应用
        app = HTMLToImageApp()
        success = app.run()
        
        return 0 if success else 1
        
    except Exception as e:
        error_msg = f"程序启动失败: {str(e)}\n\n详细错误:\n{traceback.format_exc()}"
        print(error_msg)
        try:
            messagebox.showerror("启动错误", error_msg)
        except:
            pass
        input("按回车键退出...")
        return 1


if __name__ == "__main__":
    sys.exit(main())
