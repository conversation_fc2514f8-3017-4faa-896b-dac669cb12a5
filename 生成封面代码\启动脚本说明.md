# 启动脚本说明

## 🚀 可用的启动脚本

### Windows批处理脚本

| 脚本名称 | 复杂度 | 功能特点 | 推荐场景 |
|---------|--------|----------|----------|
| **start.bat** | ⭐ | 最简单，直接启动 | 日常快速使用 |
| **run.bat** | ⭐⭐ | 基本检查 + 启动 | 一般使用 |
| **启动.bat** | ⭐⭐⭐ | 中文界面 + 详细检查 | 中文用户 |
| **启动程序.bat** | ⭐⭐⭐⭐ | 最详细的环境检查 | 故障排查 |
| **install.bat** | ⭐⭐⭐ | 依赖安装脚本 | 首次安装 |

### Python脚本

| 脚本名称 | 平台 | 功能特点 | 推荐场景 |
|---------|------|----------|----------|
| **start.py** | 跨平台 | 智能检查 + 自动安装 | 技术用户 |
| **main.py** | 跨平台 | 直接启动主程序 | 开发调试 |

### Unix脚本

| 脚本名称 | 平台 | 功能特点 | 推荐场景 |
|---------|------|----------|----------|
| **start.sh** | Linux/Mac | 彩色界面 + 完整检查 | Unix用户 |

## 📋 详细功能对比

### start.bat (推荐)
```batch
@echo off
cd /d "%~dp0"
python main.py
pause
```
- ✅ 最简单，只有4行代码
- ✅ 无编码问题
- ✅ 快速启动
- ❌ 无错误检查

### run.bat
```batch
@echo off
cd /d "%~dp0"
echo Cover Generator Tool v1.0.0
python --version >nul 2>&1
if errorlevel 1 (echo Error: Python not found)
if not exist "main.py" (echo Error: main.py not found)
python main.py
pause
```
- ✅ 基本环境检查
- ✅ 友好的错误提示
- ✅ 无编码问题
- ✅ 适合大多数用户

### 启动.bat
```batch
@echo off
setlocal enabledelayedexpansion
chcp 65001 >nul 2>&1
# 详细的检查流程
# 支持中文显示
```
- ✅ 详细的检查步骤
- ✅ 进度显示
- ✅ 依赖检查
- ⚠️ 可能有编码问题

### 启动程序.bat
```batch
@echo off
chcp 65001 >nul 2>&1
# 最完整的环境检查
# 美观的界面显示
# 详细的错误诊断
```
- ✅ 最完整的检查
- ✅ 美观的界面
- ✅ 详细的错误信息
- ⚠️ 可能有编码问题

## 🎯 使用建议

### 首次使用
1. 运行 `install.bat` 安装依赖
2. 使用 `start.bat` 快速启动

### 日常使用
- **简单用户**: 双击 `start.bat`
- **需要检查**: 双击 `run.bat`
- **故障排查**: 双击 `启动程序.bat`

### 开发调试
- 命令行运行 `python main.py`
- 或使用 `python start.py`

## 🔧 故障排除

### 编码问题
如果看到乱码，使用：
- `start.bat` (无中文字符)
- `run.bat` (英文界面)

### Python未找到
1. 检查Python是否安装
2. 检查PATH环境变量
3. 使用完整路径启动

### 依赖缺失
1. 运行 `install.bat`
2. 或手动安装: `pip install -r requirements.txt`

### 文件缺失
1. 检查是否在正确目录
2. 确保 `main.py` 存在
3. 检查项目文件完整性

## 📊 性能对比

| 脚本 | 启动速度 | 检查完整性 | 用户友好性 | 稳定性 |
|------|----------|------------|------------|--------|
| start.bat | ⭐⭐⭐⭐⭐ | ⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| run.bat | ⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| 启动.bat | ⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐ |
| 启动程序.bat | ⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ |

## 🎉 总结

- **日常使用**: `start.bat` 最简单快速
- **新手用户**: `run.bat` 有基本检查
- **故障排查**: `启动程序.bat` 最详细
- **跨平台**: `start.py` 通用性最好

选择适合您需求的启动方式，享受千帆封面图生成工具！
