@echo off
REM 设置UTF-8编码
chcp 65001 >nul 2>&1

REM 获取脚本所在目录
set SCRIPT_DIR=%~dp0

REM 切换到脚本目录
cd /d "%SCRIPT_DIR%"

echo ========================================
echo Cover Generator Tool - Install Script
echo ========================================
echo.

REM 检查Python是否可用
echo Checking Python environment...
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python not found
    echo.
    echo Please install Python 3.8 or higher
    echo Download: https://www.python.org/
    echo Make sure to check "Add Python to PATH" during installation
    echo.
    pause
    exit /b 1
)

python --version
echo Python environment OK
echo.

REM 检查pip是否可用
echo Checking pip...
pip --version >nul 2>&1
if errorlevel 1 (
    echo Error: pip not found, please reinstall Python
    pause
    exit /b 1
)
echo pip OK
echo.

REM 检查requirements.txt是否存在
if not exist "requirements.txt" (
    echo Error: requirements.txt not found
    echo Current directory: %CD%
    pause
    exit /b 1
)

echo Installing Python dependencies...
pip install -r requirements.txt
if errorlevel 1 (
    echo.
    echo Installation failed, trying with China mirror...
    pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
    if errorlevel 1 (
        echo.
        echo Installation failed, please check network or install manually
        pause
        exit /b 1
    )
)

echo.
echo Installing playwright browser...
playwright install chromium
if errorlevel 1 (
    echo.
    echo Playwright browser installation failed
    echo You can install it manually later: playwright install chromium
    echo.
)

echo.
echo ========================================
echo Installation Complete!
echo ========================================
echo.
echo Usage:
echo 1. Double-click run.bat to start
echo 2. Or run: python main.py
echo.
echo For help, see README.md
echo.
pause
