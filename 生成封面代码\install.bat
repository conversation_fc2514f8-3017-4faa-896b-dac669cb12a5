@echo off
chcp 65001 >nul
echo ========================================
echo 千帆封面图生成工具 - 安装脚本
echo ========================================
echo.

REM 获取脚本所在目录
set SCRIPT_DIR=%~dp0

REM 切换到脚本目录
cd /d "%SCRIPT_DIR%"

REM 检查Python是否可用
echo 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误：未找到Python，请先安装Python
    echo.
    echo 请访问 https://www.python.org/ 下载并安装Python 3.8或更高版本
    echo 安装时请勾选"Add Python to PATH"选项
    pause
    exit /b 1
)

python --version
echo Python环境检查通过
echo.

REM 检查pip是否可用
echo 检查pip...
pip --version >nul 2>&1
if errorlevel 1 (
    echo 错误：未找到pip，请重新安装Python
    pause
    exit /b 1
)
echo pip检查通过
echo.

REM 检查requirements.txt是否存在
if not exist "requirements.txt" (
    echo 错误：未找到requirements.txt文件
    echo 当前目录：%CD%
    pause
    exit /b 1
)

echo 正在安装Python依赖包...
pip install -r requirements.txt
if errorlevel 1 (
    echo.
    echo 依赖包安装失败，尝试使用国内镜像源...
    pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
    if errorlevel 1 (
        echo.
        echo 依赖包安装失败，请检查网络连接或手动安装
        pause
        exit /b 1
    )
)

echo.
echo 正在安装playwright浏览器...
playwright install chromium
if errorlevel 1 (
    echo.
    echo playwright浏览器安装失败，请检查网络连接
    echo 您可以稍后手动运行: playwright install chromium
    echo.
)

echo.
echo ========================================
echo 安装完成！
echo ========================================
echo.
echo 使用方法：
echo 1. 双击 run.bat 启动程序
echo 2. 或者在此目录下运行: python main.py
echo.
echo 如果遇到问题，请查看README.md或使用指南.md
echo.
pause
