#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HTML模板处理模块
用于读取、修改和生成HTML封面文件
"""

import os
import re
from typing import Dict, List, Optional

from config.settings import APP_CONFIG
from config.themes import get_theme_by_name
from config.icons import get_random_icons


class HTMLProcessor:
    """HTML模板处理器"""
    
    def __init__(self):
        self.config = APP_CONFIG
        self.template_path = os.path.join(self.config['templates_dir'], 'cover_template.html')
        self.template_content = None
        self._load_template()
    
    def _load_template(self):
        """加载HTML模板"""
        try:
            if not os.path.exists(self.template_path):
                raise FileNotFoundError(f"模板文件不存在: {self.template_path}")
            
            with open(self.template_path, 'r', encoding='utf-8') as f:
                self.template_content = f.read()
            
            print(f"✓ HTML模板加载成功: {self.template_path}")
            
        except Exception as e:
            print(f"✗ HTML模板加载失败: {e}")
            raise
    
    def generate_cover_html(self, university: str, college: str, theme_name: str) -> str:
        """
        生成封面HTML内容
        
        Args:
            university: 大学名称
            college: 学院名称
            theme_name: 主题名称
            
        Returns:
            生成的HTML内容
        """
        try:
            if not self.template_content:
                raise ValueError("模板内容未加载")
            
            # 获取主题配置
            theme = get_theme_by_name(theme_name)
            print(f"使用主题: {theme_name}")
            
            # 获取随机图标
            icons = get_random_icons(4)
            print(f"使用图标: {[icon['name'] for icon in icons]}")
            
            # 准备替换变量
            replacements = {
                'UNIVERSITY': university,
                'COLLEGE': college,
                'PRIMARY_GRADIENT': theme['primary_gradient'],
                'TEXT_PRIMARY': theme['text_primary'],
                'TEXT_SECONDARY': theme['text_secondary'],
                'TEXT_ACCENT': theme['text_accent'],
                'BORDER_COLOR': theme['border_color'],
                'FEATURE_BORDER': theme['feature_border'],
                'DECORATION_COLOR': theme['decoration_color'],
                'ICON_1': icons[0]['svg'],
                'ICON_2': icons[1]['svg'],
                'ICON_3': icons[2]['svg'],
                'ICON_4': icons[3]['svg']
            }
            
            # 执行替换
            html_content = self.template_content
            for key, value in replacements.items():
                placeholder = f'{{{{{key}}}}}'
                html_content = html_content.replace(placeholder, str(value))
            
            # 验证是否还有未替换的占位符
            remaining_placeholders = re.findall(r'\{\{[^}]+\}\}', html_content)
            if remaining_placeholders:
                print(f"警告: 发现未替换的占位符: {remaining_placeholders}")
            
            print(f"✓ HTML内容生成成功")
            return html_content
            
        except Exception as e:
            print(f"✗ HTML内容生成失败: {e}")
            raise
    
    def save_html_file(self, html_content: str, output_path: str) -> bool:
        """
        保存HTML文件
        
        Args:
            html_content: HTML内容
            output_path: 输出路径
            
        Returns:
            是否保存成功
        """
        try:
            # 确保输出目录存在
            output_dir = os.path.dirname(output_path)
            if output_dir:
                os.makedirs(output_dir, exist_ok=True)
            
            # 保存文件
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            print(f"✓ HTML文件保存成功: {output_path}")
            return True
            
        except Exception as e:
            print(f"✗ HTML文件保存失败: {e}")
            return False
    
    def generate_and_save(self, university: str, college: str, theme_name: str, output_path: str) -> bool:
        """
        生成并保存HTML文件
        
        Args:
            university: 大学名称
            college: 学院名称
            theme_name: 主题名称
            output_path: 输出路径
            
        Returns:
            是否成功
        """
        try:
            # 生成HTML内容
            html_content = self.generate_cover_html(university, college, theme_name)
            
            # 保存文件
            return self.save_html_file(html_content, output_path)
            
        except Exception as e:
            print(f"生成并保存HTML失败: {e}")
            return False
    
    def validate_template(self) -> bool:
        """
        验证模板是否有效
        
        Returns:
            模板是否有效
        """
        try:
            if not self.template_content:
                return False
            
            # 检查必需的占位符
            required_placeholders = [
                'UNIVERSITY', 'COLLEGE', 'PRIMARY_GRADIENT',
                'TEXT_PRIMARY', 'TEXT_SECONDARY', 'TEXT_ACCENT',
                'BORDER_COLOR', 'FEATURE_BORDER', 'DECORATION_COLOR',
                'ICON_1', 'ICON_2', 'ICON_3', 'ICON_4'
            ]
            
            missing_placeholders = []
            for placeholder in required_placeholders:
                if f'{{{{{placeholder}}}}}' not in self.template_content:
                    missing_placeholders.append(placeholder)
            
            if missing_placeholders:
                print(f"模板验证失败，缺少占位符: {missing_placeholders}")
                return False
            
            print("✓ 模板验证通过")
            return True
            
        except Exception as e:
            print(f"模板验证失败: {e}")
            return False
    
    def get_template_info(self) -> Optional[Dict]:
        """
        获取模板信息
        
        Returns:
            模板信息字典
        """
        try:
            if not os.path.exists(self.template_path):
                return None
            
            # 获取文件信息
            stat = os.stat(self.template_path)
            
            # 查找所有占位符
            placeholders = re.findall(r'\{\{([^}]+)\}\}', self.template_content or '')
            
            info = {
                'template_path': self.template_path,
                'file_size': stat.st_size,
                'placeholders': list(set(placeholders)),  # 去重
                'placeholder_count': len(placeholders),
                'is_valid': self.validate_template()
            }
            
            return info
            
        except Exception as e:
            print(f"获取模板信息失败: {e}")
            return None
    
    def preview_html(self, university: str, college: str, theme_name: str, lines: int = 50) -> Optional[str]:
        """
        预览生成的HTML内容
        
        Args:
            university: 大学名称
            college: 学院名称
            theme_name: 主题名称
            lines: 预览行数
            
        Returns:
            预览内容
        """
        try:
            html_content = self.generate_cover_html(university, college, theme_name)
            
            # 分割成行并取前N行
            html_lines = html_content.split('\n')
            preview_lines = html_lines[:lines]
            
            preview_content = '\n'.join(preview_lines)
            if len(html_lines) > lines:
                preview_content += f'\n... (还有 {len(html_lines) - lines} 行)'
            
            return preview_content
            
        except Exception as e:
            print(f"预览HTML失败: {e}")
            return None


def test_html_processor():
    """测试HTML处理器"""
    try:
        processor = HTMLProcessor()
        
        # 测试模板验证
        if not processor.validate_template():
            print("模板验证失败")
            return
        
        # 测试生成HTML
        html_content = processor.generate_cover_html(
            university="电子科技大学",
            college="经济与管理学院",
            theme_name="科技蓝"
        )
        
        print(f"生成的HTML长度: {len(html_content)} 字符")
        
        # 测试保存文件
        test_output = "test_cover.html"
        if processor.save_html_file(html_content, test_output):
            print(f"测试文件保存成功: {test_output}")
            
            # 清理测试文件
            if os.path.exists(test_output):
                os.remove(test_output)
        
        print("HTML处理器测试完成")
        
    except Exception as e:
        print(f"测试失败: {e}")


if __name__ == "__main__":
    test_html_processor()
