@echo off
setlocal enabledelayedexpansion

REM 设置代码页为UTF-8
chcp 65001 >nul 2>&1

REM 获取脚本所在目录
set "SCRIPT_DIR=%~dp0"

REM 切换到脚本目录
pushd "%SCRIPT_DIR%"

REM 设置窗口标题
title Cover Generator Tool

echo.
echo ================================================
echo    Cover Generator Tool v1.0.0
echo ================================================
echo.

REM 检查Python环境
echo [1/4] Checking Python...
python --version >nul 2>&1
if !errorlevel! neq 0 (
    echo [ERROR] Python not found
    echo.
    echo Please install Python 3.8+ from:
    echo https://www.python.org/
    echo.
    echo Make sure to add Python to PATH
    goto :error_exit
)

for /f "tokens=2" %%i in ('python --version 2^>^&1') do set "PYTHON_VER=%%i"
echo [OK] Python !PYTHON_VER!

REM 检查主程序文件
echo [2/4] Checking files...
if not exist "main.py" (
    echo [ERROR] main.py not found
    echo Current directory: !CD!
    goto :error_exit
)
echo [OK] Program files found

REM 检查依赖（简单检查）
echo [3/4] Checking dependencies...
python -c "import tkinter, pandas, openai" >nul 2>&1
if !errorlevel! neq 0 (
    echo [WARNING] Some dependencies missing
    echo Run install.bat first or continue anyway
    echo.
    set /p "choice=Continue? (y/n): "
    if /i "!choice!" neq "y" goto :error_exit
) else (
    echo [OK] Dependencies available
)

REM 启动程序
echo [4/4] Starting application...
echo ================================================
echo.

python main.py
set "EXIT_CODE=!errorlevel!"

echo.
echo ================================================
if !EXIT_CODE! equ 0 (
    echo [OK] Program exited normally
) else (
    echo [WARNING] Program exited with code !EXIT_CODE!
)

popd
pause
exit /b !EXIT_CODE!

:error_exit
echo.
echo ================================================
echo [FAILED] Cannot start application
echo.
echo Solutions:
echo 1. Install Python 3.8+ from python.org
echo 2. Run install.bat to install dependencies
echo 3. Make sure you're in the correct directory
echo.
echo For help, see README.md
echo ================================================
popd
pause
exit /b 1
