#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图标配置文件
定义各种SVG图标，用于封面的四个特色模块
"""

import random

# SVG图标库 - 扩展版本，适合各类学院的图标
ICON_LIBRARY = {
    # 数据分析类图标
    'chart_bar': {
        'name': '柱状图',
        'description': '数据分析、统计图表',
        'category': '数据分析',
        'svg': '''<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M7.5 14.25v2.25m3-4.5v4.5m3-6.75v6.75m3-9v9M6 20.25h12A2.25 2.25 0 0 0 20.25 18V6A2.25 2.25 0 0 0 18 3.75H6A2.25 2.25 0 0 0 3.75 6v12A2.25 2.25 0 0 0 6 20.25Z" />
        </svg>'''
    },

    'chart_pie': {
        'name': '饼图',
        'description': '数据统计、比例分析',
        'category': '数据分析',
        'svg': '''<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M10.5 6a7.5 7.5 0 1 0 7.5 7.5h-7.5V6Z" />
            <path stroke-linecap="round" stroke-linejoin="round" d="M13.5 10.5H21A7.5 7.5 0 0 0 13.5 3v7.5Z" />
        </svg>'''
    },

    'trending_up': {
        'name': '上升趋势',
        'description': '增长、发展趋势',
        'category': '数据分析',
        'svg': '''<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 18 9 11.25l4.306 4.306a11.95 11.95 0 0 1 5.814-5.518l2.74-1.22m0 0-5.94-2.281m5.94 2.28-2.28 5.941" />
        </svg>'''
    },

    # 科技类图标
    'cpu_chip': {
        'name': '芯片',
        'description': '科技、电子技术',
        'category': '科技',
        'svg': '''<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 3v1.5M4.5 8.25H3m18 0h-1.5M4.5 12H3m18 0h-1.5m-16.5 3.75H3m18 0h-1.5M8.25 19.5V21M12 3v1.5m0 15V21m3.75-18v1.5m0 15V21m-9-1.5h10.5a3 3 0 0 0 3-3V7.5a3 3 0 0 0-3-3H6.75a3 3 0 0 0-3 3v9a3 3 0 0 0 3 3Z" />
        </svg>'''
    },

    'computer_desktop': {
        'name': '台式电脑',
        'description': '计算机、信息技术',
        'category': '科技',
        'svg': '''<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M9 17.25v1.007a3 3 0 0 1-.879 2.122L7.5 21h9l-.621-.621A3 3 0 0 1 15 18.257V17.25m6-12V15a2.25 2.25 0 0 1-2.25 2.25H5.25A2.25 2.25 0 0 1 3 15V5.25a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 5.25Z" />
        </svg>'''
    },

    'cog': {
        'name': '齿轮',
        'description': '工程、机械',
        'category': '科技',
        'svg': '''<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.*************.083.22.127.325.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 0 1 1.37.49l1.296 2.247a1.125 1.125 0 0 1-.26 1.431l-1.003.827c-.293.241-.438.613-.43.992a6.759 6.759 0 0 1 0 .255c-.008.378.137.75.43.991l1.004.827c.424.35.534.955.26 1.43l-1.298 2.247a1.125 1.125 0 0 1-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.57 6.57 0 0 1-.22.128c-.331.183-.581.495-.644.869l-.213 1.281c-.09.543-.56.94-1.11.94h-2.594c-.55 0-1.019-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 0 1-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 0 1-1.369-.49l-1.297-2.247a1.125 1.125 0 0 1 .26-1.431l1.004-.827c.292-.24.437-.613.43-.991a6.932 6.932 0 0 1 0-.255c.007-.38-.138-.751-.43-.992l-1.004-.827a1.125 1.125 0 0 1-.26-1.43l1.297-2.247a1.125 1.125 0 0 1 1.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.086.22-.128.332-.183.582-.495.644-.869l.214-1.28Z" />
            <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" />
        </svg>'''
    },

    # 商务管理类图标
    'briefcase': {
        'name': '公文包',
        'description': '商务、管理',
        'category': '商务',
        'svg': '''<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M20.25 14.15v4.25c0 1.094-.787 2.036-1.872 2.18-2.087.277-4.216.42-6.378.42s-4.291-.143-6.378-.42c-1.085-.144-1.872-1.086-1.872-2.18v-4.25m16.5 0a2.18 2.18 0 0 0 .75-1.661V8.706c0-1.081-.768-2.015-1.837-2.175a48.114 48.114 0 0 0-3.413-.387m4.5 8.006c-.194.165-.42.295-.673.38A23.978 23.978 0 0 1 12 15.75c-2.648 0-5.195-.429-7.577-1.22a2.016 2.016 0 0 1-.673-.38m0 0A2.18 2.18 0 0 1 3 12.489V8.706c0-1.081.768-2.015 1.837-2.175a48.111 48.111 0 0 1 3.413-.387m7.5 0V5.25A2.25 2.25 0 0 0 13.5 3h-3a2.25 2.25 0 0 0-2.25 2.25v.894m7.5 0a48.667 48.667 0 0 0-7.5 0M12 12.75h.008v.008H12v-.008Z" />
        </svg>'''
    },

    'banknotes': {
        'name': '钞票',
        'description': '金融、财务',
        'category': '商务',
        'svg': '''<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 18.75a60.07 60.07 0 0 1 15.797 2.101c.727.198 1.453-.342 1.453-1.096V18.75M3.75 4.5v.75A.75.75 0 0 1 3 6h-.75m0 0v-.375c0-.621.504-1.125 1.125-1.125H3.75m0 0h15.75m0 0h.375c.621 0 1.125.504 1.125 1.125v.375m0 0a.75.75 0 0 1-.75.75H21m0 0v15.75m0 0h.375c.621 0 1.125-.504 1.125-1.125v-.375m0 0a.75.75 0 0 1-.75-.75H21m0 0H3.75m0 0h-.375a1.125 1.125 0 0 1-1.125-1.125v-.375m0 0a.75.75 0 0 1 .75-.75H3.75m0 0v-15.75" />
        </svg>'''
    },

    'building_storefront': {
        'name': '商店',
        'description': '零售、商业',
        'category': '商务',
        'svg': '''<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M13.5 21v-7.5a.75.75 0 0 1 .75-.75h3a.75.75 0 0 1 .75.75V21m-4.5 0H2.36m11.14 0H18m0 0h3.64m-1.39 0V9.349M3.75 21V9.349m0 0a3.001 3.001 0 0 0 3.75-.615A2.993 2.993 0 0 0 9.75 9.75c.896 0 1.7-.393 2.25-1.016a2.993 2.993 0 0 0 2.25 1.016c.896 0 1.7-.393 2.25-1.016a3.001 3.001 0 0 0 3.75.614m-16.5 0a3.004 3.004 0 0 1-.621-4.72L4.318 3.44A1.5 1.5 0 0 1 5.378 3h13.243a1.5 1.5 0 0 1 1.06.44l1.19 1.189a3 3 0 0 1-.621 4.72M6.75 18h3.75a.75.75 0 0 0 .75-.75V13.5a.75.75 0 0 0-.75-.75H6.75a.75.75 0 0 0-.75.75v3.75c0 .414.336.75.75.75Z" />
        </svg>'''
    },

    # 财务会计类图标
    'calculator': {
        'name': '计算器',
        'description': '财务、会计',
        'category': '财务',
        'svg': '''<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 15.75V18m-7.5-6.75h.008v.008H8.25v-.008Zm0 2.25h.008v.008H8.25V13.5Zm0 2.25h.008v.008H8.25v-.008Zm0 2.25h.008V18H8.25v-.75Zm2.25-4.5h.008v.008H10.5v-.008Zm0 2.25h.008v.008H10.5V13.5Zm0 2.25h.008v.008H10.5v-.008Zm0 2.25h.008V18H10.5v-.75Zm2.25-4.5h.008v.008H12.75v-.008Zm0 2.25h.008v.008H12.75V13.5Zm0 2.25h.008v.008H12.75v-.008Zm0 2.25h.008V18h-.008v-.75Zm2.25-4.5h.008v.008H15v-.008Zm0 2.25h.008v.008H15V13.5Zm-7.5-9v2.25c0 .414.336.75.75.75h2.25a.75.75 0 0 0 .75-.75V6a.75.75 0 0 0-.75-.75H8.25A.75.75 0 0 0 7.5 6Zm7.5 0v2.25c0 .414.336.75.75.75h2.25a.75.75 0 0 0 .75-.75V6a.75.75 0 0 0-.75-.75H15.75a.75.75 0 0 0-.75.75ZM6 3.75A2.25 2.25 0 0 1 8.25 1.5h7.5A2.25 2.25 0 0 1 18 3.75v16.5A2.25 2.25 0 0 1 15.75 22.5h-7.5A2.25 2.25 0 0 1 6 20.25V3.75Z" />
        </svg>'''
    },

    'currency_dollar': {
        'name': '美元符号',
        'description': '金融、货币',
        'category': '财务',
        'svg': '''<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
        </svg>'''
    },

    'scale': {
        'name': '天平',
        'description': '法律、公正',
        'category': '法律',
        'svg': '''<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 3v17.25m0 0c-1.472 0-2.882.265-4.185.75M12 20.25c1.472 0 2.882.265 4.185.75M18.75 4.97A48.254 48.254 0 0 0 12 4.5c-2.291 0-4.545.16-6.75.47m13.5 0c1.01.143 2.01.317 3 .52m-3-.52l2.62 10.726c.122.499-.106 1.028-.589 1.202a5.988 5.988 0 0 1-2.031.352 5.988 5.988 0 0 1-2.031-.352c-.483-.174-.711-.703-.589-1.202L18.75 4.971Zm-16.5.52c.99-.203 1.99-.377 3-.52m0 0 2.62 10.726c.122.499-.106 1.028-.589 1.202a5.989 5.989 0 0 1-2.031.352 5.989 5.989 0 0 1-2.031-.352c-.483-.174-.711-.703-.589-1.202L5.25 4.971Z" />
        </svg>'''
    },

    # 教育学术类图标
    'academic_cap': {
        'name': '学士帽',
        'description': '教育、学术',
        'category': '教育',
        'svg': '''<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M4.26 10.147a60.438 60.438 0 0 0-.491 6.347A48.62 48.62 0 0 1 12 20.904a48.62 48.62 0 0 1 8.232-4.41 60.46 60.46 0 0 0-.491-6.347m-15.482 0a50.636 50.636 0 0 0-2.658-.813A59.906 59.906 0 0 1 12 3.493a59.903 59.903 0 0 1 10.399 5.84c-.896.248-1.783.52-2.658.814m-15.482 0A50.717 50.717 0 0 1 12 13.489a50.702 50.702 0 0 1 7.74-3.342M6.75 15a.75.75 0 1 0 0-********* 0 0 0 0 1.5Zm0 0v-3.675A55.378 55.378 0 0 1 12 8.443a55.381 55.381 0 0 1 5.25 2.882V15" />
        </svg>'''
    },

    'book_open': {
        'name': '打开的书',
        'description': '学习、知识',
        'category': '教育',
        'svg': '''<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25" />
        </svg>'''
    },

    'pencil_square': {
        'name': '编辑',
        'description': '写作、编辑',
        'category': '教育',
        'svg': '''<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10" />
        </svg>'''
    },

    # 文档报告类图标
    'document_chart': {
        'name': '文档图表',
        'description': '报告、数据分析',
        'category': '文档',
        'svg': '''<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z" />
        </svg>'''
    },

    'clipboard_document': {
        'name': '剪贴板文档',
        'description': '记录、文档管理',
        'category': '文档',
        'svg': '''<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 7.5V6.108c0-1.135.845-2.098 1.976-2.192.373-.03.748-.057 1.123-.08M15.75 18H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08M15.75 18.75v-1.875a3.375 3.375 0 0 0-3.375-3.375h-1.5a1.125 1.125 0 0 1-1.125-1.125v-1.5A3.375 3.375 0 0 0 6.375 7.5H5.25m11.9-3.664A2.251 2.251 0 0 0 15 2.25h-1.5a2.251 2.251 0 0 0-2.15 1.586m5.8 0c.065.21.1.433.1.664v.75h-6V4.5c0-.231.035-.454.1-.664M6.75 7.5H4.875c-.621 0-1.125.504-1.125 1.125v12c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V16.5a9 9 0 0 0-9-9Z" />
        </svg>'''
    },

    # 建筑工程类图标
    'building_office': {
        'name': '办公楼',
        'description': '企业、机构',
        'category': '建筑',
        'svg': '''<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 21h16.5M4.5 3h15l-.75 18h-13.5L4.5 3ZM7.5 6h.008v.008H7.5V6Zm0 3h.008v.008H7.5V9Zm0 3h.008v.008H7.5V12Zm0 3h.008v.008H7.5V15Zm3-9h.008v.008H10.5V6Zm0 3h.008v.008H10.5V9Zm0 3h.008v.008H10.5V12Zm0 3h.008v.008H10.5V15Zm3-9h.008v.008H13.5V6Zm0 3h.008v.008H13.5V9Zm0 3h.008v.008H13.5V12Zm0 3h.008v.008H13.5V15Zm3-9h.008v.008H16.5V6Zm0 3h.008v.008H16.5V9Zm0 3h.008v.008H16.5V12Z" />
        </svg>'''
    },

    'wrench_screwdriver': {
        'name': '工具',
        'description': '工程、维修',
        'category': '建筑',
        'svg': '''<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M11.42 15.17 17.25 21A2.652 2.652 0 0 0 21 17.25l-5.877-5.877M11.42 15.17l2.496-3.03c.317-.384.74-.626 1.208-.766M11.42 15.17l-4.655 5.653a2.548 2.548 0 1 1-3.586-3.586l6.837-5.63m5.108-.233c.55-.164 1.163-.188 1.743-.14a4.5 4.5 0 0 0 4.486-6.336l-3.276 3.277a3.004 3.004 0 0 1-2.25-2.25l3.276-3.276a4.5 4.5 0 0 0-6.336 4.486c.091 1.076-.071 2.264-.904 2.95l-.102.085m-1.745 1.437L5.909 7.5H4.5L2.25 3.75l1.5-1.5L7.5 4.5v1.409l4.26 4.26m-1.745 1.437 1.745-1.437m6.615 8.206L15.75 15.75M4.867 19.125h.008v.008h-.008v-.008Z" />
        </svg>'''
    },

    # 展示演讲类图标
    'presentation': {
        'name': '演示',
        'description': '展示、培训',
        'category': '展示',
        'svg': '''<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 3v11.25A2.25 2.25 0 0 0 6 16.5h2.25M3.75 3h-1.5m1.5 0h16.5m0 0h1.5m-1.5 0v11.25A2.25 2.25 0 0 1 18 16.5h-2.25m-7.5 0h7.5m-7.5 0-1 3m8.5-3 1 3m0 0-1-3m1 3-1-3m-9-1.5h.008v.008H8.25v-.008Zm2.25 0h.008v.008H10.5v-.008Zm2.25 0h.008v.008H12.75v-.008Zm2.25 0h.008v.008H15v-.008Zm2.25 0h.008v.008H17.25v-.008Z" />
        </svg>'''
    },

    'megaphone': {
        'name': '扩音器',
        'description': '宣传、传播',
        'category': '展示',
        'svg': '''<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M10.34 15.84c-.688-.06-1.386-.09-2.09-.09H7.5a4.5 4.5 0 1 1 0-9h.75c.704 0 1.402-.03 2.09-.09m0 9.18c.253.962.584 1.892.985 2.783.247.55.06 1.21-.463 1.511l-.657.38c-.551.318-1.26.117-1.527-.461a20.845 20.845 0 0 1-1.44-4.282m3.102.069a18.03 18.03 0 0 1-.59-4.59c0-1.586.205-3.124.59-4.59m0 9.18a23.848 23.848 0 0 1 8.835 2.535M10.34 6.66a23.847 23.847 0 0 0 8.835-2.535m0 0A23.74 23.74 0 0 0 18.795 3m.38 1.125a23.91 23.91 0 0 1 1.014 5.395m-1.014 8.855c-.118.38-.245.754-.38 1.125m.38-1.125a23.91 23.91 0 0 0 1.014-5.395m0-3.46c.495.413.811 1.035.811 1.73 0 .695-.316 1.317-.811 1.73m0-3.46a24.347 24.347 0 0 1 0 3.46" />
        </svg>'''
    },

    # 国际全球类图标
    'globe': {
        'name': '地球',
        'description': '国际、全球化',
        'category': '国际',
        'svg': '''<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 21a9.004 9.004 0 0 0 8.716-6.747M12 21a9.004 9.004 0 0 1-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3m0 18c-2.485 0-4.5-4.03-4.5-9S9.515 3 12 3m0 0a8.997 8.997 0 0 1 7.843 4.582M12 3a8.997 8.997 0 0 0-7.843 4.582m15.686 0A11.953 11.953 0 0 1 12 10.5c-2.998 0-5.74-1.1-7.843-2.918m15.686 0A8.959 8.959 0 0 1 21 12c0 .778-.099 1.533-.284 2.253m0 0A17.919 17.919 0 0 1 12 16.5c-3.162 0-6.133-.815-8.716-2.247m0 0A9.015 9.015 0 0 1 3 12c0-1.605.42-3.113 1.157-4.418" />
        </svg>'''
    },

    'language': {
        'name': '语言',
        'description': '语言学、翻译',
        'category': '国际',
        'svg': '''<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="m10.5 21 5.25-11.25L21 21m-9-3h7.5M3 5.621a48.474 48.474 0 0 1 6-.371c.22 0 .437.006.653.018M9 5.621v12.854c0 .597.237 1.17.659 1.591l.853.853c.297.297.735.297 1.032 0l.853-.853c.422-.421.659-.994.659-1.591V5.621M3 5.621C3 3.613 4.613 2 6.621 2c1.064 0 1.944.864 1.944 1.928 0 .738-.41 1.376-1.016 1.693M3 5.621 2.016 7.314C1.41 7.631 1 8.269 1 9.007c0 1.064.864 1.928 1.928 1.928h1.144" />
        </svg>'''
    },

    # 创新创意类图标
    'lightbulb': {
        'name': '灯泡',
        'description': '创新、想法',
        'category': '创新',
        'svg': '''<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 18v-5.25m0 0a6.01 6.01 0 0 0 1.5-.189m-1.5.189a6.01 6.01 0 0 1-1.5-.189m3.75 7.478a12.06 12.06 0 0 1-4.5 0m3.75-7.478v-.353a4.5 4.5 0 0 0-3.75-4.434V8.25a.75.75 0 0 0-1.5 0v.384a4.5 4.5 0 0 0-3.75 4.434v.353m7.5 0a48.667 48.667 0 0 0-7.5 0" />
        </svg>'''
    },

    'rocket_launch': {
        'name': '火箭',
        'description': '创业、启动',
        'category': '创新',
        'svg': '''<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M15.59 14.37a6 6 0 0 1-5.84 7.38v-4.8m5.84-2.58a14.98 14.98 0 0 0 6.16-12.12A14.98 14.98 0 0 0 9.631 8.41m5.96 5.96a14.926 14.926 0 0 1-5.841 2.58m-.119-8.54a6 6 0 0 0-7.381 5.84h4.8m2.581-5.84a14.927 14.927 0 0 0-2.58 5.84m2.699 2.7c-.103.021-.207.041-.311.06a15.09 15.09 0 0 1-2.448-2.448 14.9 14.9 0 0 1 .06-.312m-2.24 2.39a4.493 4.493 0 0 0-1.757 4.306 4.493 4.493 0 0 0 4.306-1.758M16.5 9a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0Z" />
        </svg>'''
    },

    'beaker': {
        'name': '烧杯',
        'description': '实验、研究',
        'category': '科研',
        'svg': '''<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M9.75 3.104v5.714a2.25 2.25 0 0 1-.659 1.591L5 14.5a2.25 2.25 0 0 0-.659 1.591v3.853a3.75 3.75 0 0 0 3.75 3.75h8.25a3.75 3.75 0 0 0 3.75-3.75v-3.853a2.25 2.25 0 0 0-.659-1.591L15.5 10.409a2.25 2.25 0 0 1-.659-1.591V3.104a48.554 48.554 0 0 0-5.092 0ZM8.25 21h7.5" />
        </svg>'''
    }
}

def get_random_icons(count=4):
    """随机获取指定数量的图标"""
    icon_keys = list(ICON_LIBRARY.keys())
    selected_keys = random.sample(icon_keys, min(count, len(icon_keys)))
    return [ICON_LIBRARY[key] for key in selected_keys]

def get_icon_by_name(icon_name):
    """根据名称获取图标"""
    return ICON_LIBRARY.get(icon_name)

def get_all_icon_names():
    """获取所有图标名称列表"""
    return list(ICON_LIBRARY.keys())

def get_icons_by_category(category):
    """根据类别获取图标列表"""
    return [name for name, icon in ICON_LIBRARY.items() if icon.get('category') == category]

def get_smart_icons(college_type, count=4):
    """
    根据学院类型智能选择图标

    Args:
        college_type: 学院类型关键词
        count: 需要的图标数量

    Returns:
        图标列表
    """
    # 定义学院类型与图标类别的映射
    type_category_map = {
        '商学': ['商务', '财务', '数据分析'],
        '经济': ['财务', '商务', '数据分析'],
        '管理': ['商务', '数据分析', '展示'],
        '金融': ['财务', '商务', '数据分析'],
        '会计': ['财务', '商务', '数据分析'],
        '计算机': ['科技', '数据分析', '创新'],
        '电子': ['科技', '创新', '数据分析'],
        '信息': ['科技', '数据分析', '展示'],
        '软件': ['科技', '创新', '数据分析'],
        '工程': ['建筑', '科技', '创新'],
        '建筑': ['建筑', '创新', '展示'],
        '土木': ['建筑', '科技', '展示'],
        '机械': ['建筑', '科技', '创新'],
        '法学': ['法律', '文档', '展示'],
        '政治': ['法律', '展示', '文档'],
        '公共': ['展示', '文档', '商务'],
        '文学': ['教育', '文档', '创新'],
        '教育': ['教育', '展示', '文档'],
        '外语': ['国际', '教育', '文档'],
        '国际': ['国际', '商务', '展示'],
        '艺术': ['创新', '展示', '教育'],
        '设计': ['创新', '展示', '科技'],
        '传媒': ['展示', '创新', '国际'],
        '医学': ['科研', '教育', '创新'],
        '生物': ['科研', '创新', '教育'],
        '化学': ['科研', '创新', '科技']
    }

    # 根据学院类型选择合适的图标类别
    preferred_categories = []
    college_lower = college_type.lower()

    for key, categories in type_category_map.items():
        if key in college_lower:
            preferred_categories.extend(categories)
            break

    # 如果没有匹配到特定类型，使用默认类别
    if not preferred_categories:
        preferred_categories = ['商务', '数据分析', '展示', '教育']

    # 收集符合类别的图标
    suitable_icons = []
    for category in preferred_categories:
        category_icons = get_icons_by_category(category)
        suitable_icons.extend(category_icons)

    # 去重
    suitable_icons = list(set(suitable_icons))

    # 如果合适的图标不够，补充随机图标
    if len(suitable_icons) < count:
        all_icons = list(ICON_LIBRARY.keys())
        remaining_icons = [icon for icon in all_icons if icon not in suitable_icons]
        suitable_icons.extend(remaining_icons)

    # 随机选择指定数量的图标
    selected_keys = random.sample(suitable_icons, min(count, len(suitable_icons)))
    return [ICON_LIBRARY[key] for key in selected_keys]

def get_all_categories():
    """获取所有图标类别"""
    categories = set()
    for icon in ICON_LIBRARY.values():
        if 'category' in icon:
            categories.add(icon['category'])
    return sorted(list(categories))
