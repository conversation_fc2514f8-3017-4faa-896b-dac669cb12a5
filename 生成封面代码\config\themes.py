#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主题色配置文件
定义各种主题色方案，用于不同类型的学院
"""

# 主题色配置
THEME_COLORS = {
    '科技蓝': {
        'name': '科技蓝',
        'description': '适合理工科、计算机、电子信息类学院',
        'primary_gradient': 'linear-gradient(145deg, #1e3a8a 0%, #3b82f6 100%)',
        'accent_color': '#fbbf24',  # 金色点缀
        'text_primary': '#FFFFFF',
        'text_secondary': '#dbeafe',
        'text_accent': '#fbbf24',
        'border_color': 'rgba(251, 191, 36, 0.3)',
        'feature_border': 'rgba(251, 191, 36, 0.6)',
        'decoration_color': 'rgba(251, 191, 36, 0.12)'
    },
    
    '商务金': {
        'name': '商务金',
        'description': '适合商学院、经济管理类学院',
        'primary_gradient': 'linear-gradient(145deg, #92400e 0%, #d97706 100%)',
        'accent_color': '#fbbf24',
        'text_primary': '#FFFFFF',
        'text_secondary': '#fed7aa',
        'text_accent': '#fbbf24',
        'border_color': 'rgba(251, 191, 36, 0.4)',
        'feature_border': 'rgba(251, 191, 36, 0.7)',
        'decoration_color': 'rgba(251, 191, 36, 0.15)'
    },
    
    '学术绿': {
        'name': '学术绿',
        'description': '适合文学院、教育学院、人文社科类',
        'primary_gradient': 'linear-gradient(145deg, #14532d 0%, #16a34a 100%)',
        'accent_color': '#fbbf24',
        'text_primary': '#FFFFFF',
        'text_secondary': '#bbf7d0',
        'text_accent': '#fbbf24',
        'border_color': 'rgba(251, 191, 36, 0.3)',
        'feature_border': 'rgba(251, 191, 36, 0.6)',
        'decoration_color': 'rgba(251, 191, 36, 0.12)'
    },
    
    '医学红': {
        'name': '医学红',
        'description': '适合医学院、生命科学类学院',
        'primary_gradient': 'linear-gradient(145deg, #991b1b 0%, #dc2626 100%)',
        'accent_color': '#fbbf24',
        'text_primary': '#FFFFFF',
        'text_secondary': '#fecaca',
        'text_accent': '#fbbf24',
        'border_color': 'rgba(251, 191, 36, 0.3)',
        'feature_border': 'rgba(251, 191, 36, 0.6)',
        'decoration_color': 'rgba(251, 191, 36, 0.12)'
    },
    
    '法政紫': {
        'name': '法政紫',
        'description': '适合法学院、政治学院、公共管理类',
        'primary_gradient': 'linear-gradient(145deg, #581c87 0%, #7c3aed 100%)',
        'accent_color': '#fbbf24',
        'text_primary': '#FFFFFF',
        'text_secondary': '#ddd6fe',
        'text_accent': '#fbbf24',
        'border_color': 'rgba(251, 191, 36, 0.3)',
        'feature_border': 'rgba(251, 191, 36, 0.6)',
        'decoration_color': 'rgba(251, 191, 36, 0.12)'
    },
    
    '艺术橙': {
        'name': '艺术橙',
        'description': '适合艺术学院、设计学院、传媒类',
        'primary_gradient': 'linear-gradient(145deg, #c2410c 0%, #ea580c 100%)',
        'accent_color': '#fbbf24',
        'text_primary': '#FFFFFF',
        'text_secondary': '#fed7aa',
        'text_accent': '#fbbf24',
        'border_color': 'rgba(251, 191, 36, 0.3)',
        'feature_border': 'rgba(251, 191, 36, 0.6)',
        'decoration_color': 'rgba(251, 191, 36, 0.12)'
    },
    
    '工程灰': {
        'name': '工程灰',
        'description': '适合工程学院、建筑学院、土木类',
        'primary_gradient': 'linear-gradient(145deg, #374151 0%, #6b7280 100%)',
        'accent_color': '#fbbf24',
        'text_primary': '#FFFFFF',
        'text_secondary': '#d1d5db',
        'text_accent': '#fbbf24',
        'border_color': 'rgba(251, 191, 36, 0.3)',
        'feature_border': 'rgba(251, 191, 36, 0.6)',
        'decoration_color': 'rgba(251, 191, 36, 0.12)'
    }
}

# 默认主题色（如果AI无法识别时使用）
DEFAULT_THEME = '科技蓝'

def get_theme_by_name(theme_name):
    """根据主题名称获取主题配置"""
    return THEME_COLORS.get(theme_name, THEME_COLORS[DEFAULT_THEME])

def get_all_theme_names():
    """获取所有主题名称列表"""
    return list(THEME_COLORS.keys())

def get_theme_description(theme_name):
    """获取主题描述"""
    theme = THEME_COLORS.get(theme_name)
    return theme['description'] if theme else None
