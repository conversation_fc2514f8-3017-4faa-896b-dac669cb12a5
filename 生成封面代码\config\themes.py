#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主题色配置模块 - 新版本
支持动态生成同色系的不同颜色变体
"""

import random
import hashlib

# 基础色系配置 - 每个色系一个基础模板
BASE_COLOR_SCHEMES = {
    '蓝色系': {
        'category': '蓝色系',
        'base_colors': ['#1e3a8a', '#3b82f6'],  # 深蓝到亮蓝
        'accent_base': '#fbbf24',  # 金色强调
        'text_primary': '#FFFFFF',
        'text_secondary_base': '#dbeafe',
    },
    
    '绿色系': {
        'category': '绿色系',
        'base_colors': ['#14532d', '#16a34a'],  # 深绿到亮绿
        'accent_base': '#fbbf24',  # 金色强调
        'text_primary': '#FFFFFF',
        'text_secondary_base': '#bbf7d0',
    },
    
    '红色系': {
        'category': '红色系',
        'base_colors': ['#991b1b', '#dc2626'],  # 深红到亮红
        'accent_base': '#fbbf24',  # 金色强调
        'text_primary': '#FFFFFF',
        'text_secondary_base': '#fecaca',
    },
    
    '紫色系': {
        'category': '紫色系',
        'base_colors': ['#581c87', '#7c3aed'],  # 深紫到亮紫
        'accent_base': '#fbbf24',  # 金色强调
        'text_primary': '#FFFFFF',
        'text_secondary_base': '#ddd6fe',
    },
    
    '橙色系': {
        'category': '橙色系',
        'base_colors': ['#c2410c', '#ea580c'],  # 深橙到亮橙
        'accent_base': '#fbbf24',  # 金色强调
        'text_primary': '#FFFFFF',
        'text_secondary_base': '#fed7aa',
    },
    
    '黄色系': {
        'category': '黄色系',
        'base_colors': ['#92400e', '#d97706'],  # 深黄到亮黄
        'accent_base': '#fbbf24',  # 金色强调
        'text_primary': '#FFFFFF',
        'text_secondary_base': '#fed7aa',
    },
    
    '灰色系': {
        'category': '灰色系',
        'base_colors': ['#374151', '#6b7280'],  # 深灰到亮灰
        'accent_base': '#fbbf24',  # 金色强调
        'text_primary': '#FFFFFF',
        'text_secondary_base': '#d1d5db',
    },
    
    '青色系': {
        'category': '青色系',
        'base_colors': ['#155e75', '#0891b2'],  # 深青到亮青
        'accent_base': '#22d3ee',  # 青色强调
        'text_primary': '#FFFFFF',
        'text_secondary_base': '#cffafe',
    }
}

# 默认主题色
DEFAULT_THEME = '蓝色系'

def hex_to_rgb(hex_color):
    """将十六进制颜色转换为RGB"""
    hex_color = hex_color.lstrip('#')
    return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))

def rgb_to_hex(rgb):
    """将RGB颜色转换为十六进制"""
    return f"#{rgb[0]:02x}{rgb[1]:02x}{rgb[2]:02x}"

def adjust_color_brightness(hex_color, factor):
    """调整颜色亮度"""
    r, g, b = hex_to_rgb(hex_color)
    
    # 调整亮度
    r = max(0, min(255, int(r * factor)))
    g = max(0, min(255, int(g * factor)))
    b = max(0, min(255, int(b * factor)))
    
    return rgb_to_hex((r, g, b))

def adjust_color_hue(hex_color, hue_shift):
    """调整颜色色相"""
    import colorsys
    
    r, g, b = hex_to_rgb(hex_color)
    r, g, b = r/255.0, g/255.0, b/255.0
    
    h, s, v = colorsys.rgb_to_hsv(r, g, b)
    h = (h + hue_shift) % 1.0  # 色相环绕
    
    r, g, b = colorsys.hsv_to_rgb(h, s, v)
    r, g, b = int(r*255), int(g*255), int(b*255)
    
    return rgb_to_hex((r, g, b))

def generate_random_color_variant(base_scheme, seed=None):
    """基于基础色系生成随机颜色变体"""
    if seed:
        # 使用种子确保同一输入产生相同输出
        random.seed(int(hashlib.md5(seed.encode()).hexdigest()[:8], 16))
    
    # 随机调整因子
    brightness_factor1 = random.uniform(0.8, 1.2)
    brightness_factor2 = random.uniform(0.8, 1.2)
    hue_shift = random.uniform(-0.05, 0.05)  # 小幅度色相调整
    
    # 调整基础颜色
    color1 = adjust_color_brightness(base_scheme['base_colors'][0], brightness_factor1)
    color1 = adjust_color_hue(color1, hue_shift)
    
    color2 = adjust_color_brightness(base_scheme['base_colors'][1], brightness_factor2)
    color2 = adjust_color_hue(color2, hue_shift)
    
    # 调整强调色
    accent_factor = random.uniform(0.9, 1.1)
    accent_color = adjust_color_brightness(base_scheme['accent_base'], accent_factor)
    
    # 调整次要文本颜色
    secondary_factor = random.uniform(0.95, 1.05)
    text_secondary = adjust_color_brightness(base_scheme['text_secondary_base'], secondary_factor)
    
    return {
        'name': f"{base_scheme['category']}_变体",
        'category': base_scheme['category'],
        'primary_gradient': f'linear-gradient(145deg, {color1} 0%, {color2} 100%)',
        'accent_color': accent_color,
        'text_primary': base_scheme['text_primary'],
        'text_secondary': text_secondary,
        'text_accent': accent_color,
        'border_color': f'rgba({", ".join(map(str, hex_to_rgb(accent_color)))}, 0.3)',
        'feature_border': f'rgba({", ".join(map(str, hex_to_rgb(accent_color)))}, 0.6)',
        'decoration_color': f'rgba({", ".join(map(str, hex_to_rgb(accent_color)))}, 0.12)'
    }

def get_color_scheme_for_university(university, college):
    """为大学学院生成颜色方案"""
    # 使用大学名称作为种子，确保同校同色系
    university_seed = hashlib.md5(university.encode()).hexdigest()[:8]
    random.seed(int(university_seed, 16))
    
    # 随机选择一个色系
    color_scheme_name = random.choice(list(BASE_COLOR_SCHEMES.keys()))
    base_scheme = BASE_COLOR_SCHEMES[color_scheme_name]
    
    # 使用大学+学院名称作为种子生成变体
    college_seed = f"{university}_{college}"
    variant = generate_random_color_variant(base_scheme, college_seed)
    
    return variant

# 兼容性函数
def get_all_theme_names():
    """获取所有色系名称"""
    return list(BASE_COLOR_SCHEMES.keys())

def get_themes_by_category(category):
    """根据颜色系列获取主题列表"""
    return [category] if category in BASE_COLOR_SCHEMES else []

def get_theme_by_name(theme_name):
    """根据主题名称获取主题配置"""
    if theme_name in BASE_COLOR_SCHEMES:
        return generate_random_color_variant(BASE_COLOR_SCHEMES[theme_name])
    return generate_random_color_variant(BASE_COLOR_SCHEMES[DEFAULT_THEME])

# 动态生成THEME_COLORS用于兼容性
THEME_COLORS = {}
for scheme_name, scheme_data in BASE_COLOR_SCHEMES.items():
    THEME_COLORS[scheme_name] = generate_random_color_variant(scheme_data)
