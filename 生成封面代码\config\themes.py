#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主题色配置模块 - 新版本
支持动态生成同色系的不同颜色变体
"""

import random
import hashlib

# 基础色系配置 - 色系间差异很大，同色系内变化明显
BASE_COLOR_SCHEMES = {
    '深蓝色系': {
        'category': '深蓝色系',
        'base_colors': ['#0f172a', '#1e40af'],  # 极深蓝到标准蓝
        'accent_base': '#fbbf24',  # 金色强调
        'text_primary': '#FFFFFF',
        'text_secondary_base': '#dbeafe',
        'color_variants': [  # 预定义的颜色变体，确保差异明显
            ['#0f172a', '#1e40af'],  # 深蓝
            ['#1e3a8a', '#3b82f6'],  # 中蓝
            ['#1d4ed8', '#60a5fa'],  # 亮蓝
            ['#2563eb', '#93c5fd'],  # 浅蓝
            ['#1e40af', '#bfdbfe'],  # 极浅蓝
        ]
    },

    '翠绿色系': {
        'category': '翠绿色系',
        'base_colors': ['#064e3b', '#059669'],  # 深绿到翠绿
        'accent_base': '#fbbf24',  # 金色强调
        'text_primary': '#FFFFFF',
        'text_secondary_base': '#bbf7d0',
        'color_variants': [
            ['#064e3b', '#059669'],  # 深翠绿
            ['#14532d', '#16a34a'],  # 中绿
            ['#166534', '#22c55e'],  # 亮绿
            ['#15803d', '#4ade80'],  # 浅绿
            ['#16a34a', '#86efac'],  # 极浅绿
        ]
    },

    '火红色系': {
        'category': '火红色系',
        'base_colors': ['#7f1d1d', '#dc2626'],  # 深红到火红
        'accent_base': '#fbbf24',  # 金色强调
        'text_primary': '#FFFFFF',
        'text_secondary_base': '#fecaca',
        'color_variants': [
            ['#7f1d1d', '#dc2626'],  # 深红
            ['#991b1b', '#ef4444'],  # 中红
            ['#b91c1c', '#f87171'],  # 亮红
            ['#dc2626', '#fca5a5'],  # 浅红
            ['#ef4444', '#fecaca'],  # 极浅红
        ]
    },

    '神秘紫系': {
        'category': '神秘紫系',
        'base_colors': ['#3c1361', '#7c3aed'],  # 深紫到神秘紫
        'accent_base': '#fbbf24',  # 金色强调
        'text_primary': '#FFFFFF',
        'text_secondary_base': '#ddd6fe',
        'color_variants': [
            ['#3c1361', '#7c3aed'],  # 深紫
            ['#581c87', '#8b5cf6'],  # 中紫
            ['#6b21a8', '#a855f7'],  # 亮紫
            ['#7c3aed', '#c084fc'],  # 浅紫
            ['#8b5cf6', '#ddd6fe'],  # 极浅紫
        ]
    },

    '活力橙系': {
        'category': '活力橙系',
        'base_colors': ['#9a3412', '#ea580c'],  # 深橙到活力橙
        'accent_base': '#fbbf24',  # 金色强调
        'text_primary': '#FFFFFF',
        'text_secondary_base': '#fed7aa',
        'color_variants': [
            ['#9a3412', '#ea580c'],  # 深橙
            ['#c2410c', '#f97316'],  # 中橙
            ['#ea580c', '#fb923c'],  # 亮橙
            ['#f97316', '#fdba74'],  # 浅橙
            ['#fb923c', '#fed7aa'],  # 极浅橙
        ]
    },

    '商务灰系': {
        'category': '商务灰系',
        'base_colors': ['#1f2937', '#6b7280'],  # 深灰到商务灰
        'accent_base': '#3b82f6',  # 蓝色强调
        'text_primary': '#FFFFFF',
        'text_secondary_base': '#d1d5db',
        'color_variants': [
            ['#1f2937', '#6b7280'],  # 深灰
            ['#374151', '#9ca3af'],  # 中灰
            ['#4b5563', '#d1d5db'],  # 亮灰
            ['#6b7280', '#e5e7eb'],  # 浅灰
            ['#9ca3af', '#f3f4f6'],  # 极浅灰
        ]
    }
}

# 默认主题色
DEFAULT_THEME = '蓝色系'

def hex_to_rgb(hex_color):
    """将十六进制颜色转换为RGB"""
    hex_color = hex_color.lstrip('#')
    return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))

def rgb_to_hex(rgb):
    """将RGB颜色转换为十六进制"""
    return f"#{rgb[0]:02x}{rgb[1]:02x}{rgb[2]:02x}"

def adjust_color_brightness(hex_color, factor):
    """调整颜色亮度"""
    r, g, b = hex_to_rgb(hex_color)
    
    # 调整亮度
    r = max(0, min(255, int(r * factor)))
    g = max(0, min(255, int(g * factor)))
    b = max(0, min(255, int(b * factor)))
    
    return rgb_to_hex((r, g, b))

def adjust_color_hue(hex_color, hue_shift):
    """调整颜色色相"""
    import colorsys
    
    r, g, b = hex_to_rgb(hex_color)
    r, g, b = r/255.0, g/255.0, b/255.0
    
    h, s, v = colorsys.rgb_to_hsv(r, g, b)
    h = (h + hue_shift) % 1.0  # 色相环绕
    
    r, g, b = colorsys.hsv_to_rgb(h, s, v)
    r, g, b = int(r*255), int(g*255), int(b*255)
    
    return rgb_to_hex((r, g, b))

def generate_color_variant_for_college(base_scheme, college_index):
    """为学院生成明显不同的颜色变体"""
    # 使用预定义的颜色变体，确保差异明显
    variants = base_scheme.get('color_variants', [base_scheme['base_colors']])

    # 根据学院索引选择变体，循环使用
    variant_index = college_index % len(variants)
    selected_variant = variants[variant_index]

    color1, color2 = selected_variant

    # 强调色保持一致
    accent_color = base_scheme['accent_base']
    text_secondary = base_scheme['text_secondary_base']

    return {
        'name': f"{base_scheme['category']}_变体{variant_index + 1}",
        'category': base_scheme['category'],
        'primary_gradient': f'linear-gradient(145deg, {color1} 0%, {color2} 100%)',
        'accent_color': accent_color,
        'text_primary': base_scheme['text_primary'],
        'text_secondary': text_secondary,
        'text_accent': accent_color,
        'border_color': f'rgba({", ".join(map(str, hex_to_rgb(accent_color)))}, 0.3)',
        'feature_border': f'rgba({", ".join(map(str, hex_to_rgb(accent_color)))}, 0.6)',
        'decoration_color': f'rgba({", ".join(map(str, hex_to_rgb(accent_color)))}, 0.12)'
    }

def generate_random_color_variant(base_scheme, seed=None):
    """基于基础色系生成随机颜色变体（兼容性函数）"""
    if seed:
        # 使用种子生成一个学院索引
        college_index = int(hashlib.md5(seed.encode()).hexdigest()[:8], 16) % 100
    else:
        college_index = random.randint(0, 99)

    return generate_color_variant_for_college(base_scheme, college_index)

def get_color_scheme_for_university(university, college):
    """为大学学院生成颜色方案"""
    # 使用大学名称作为种子，确保同校同色系
    university_seed = hashlib.md5(university.encode()).hexdigest()[:8]
    random.seed(int(university_seed, 16))

    # 随机选择一个色系
    color_scheme_name = random.choice(list(BASE_COLOR_SCHEMES.keys()))
    base_scheme = BASE_COLOR_SCHEMES[color_scheme_name]

    # 使用学院名称生成一个索引，确保同校不同学院有不同颜色
    college_seed = hashlib.md5(f"{university}_{college}".encode()).hexdigest()[:8]
    college_index = int(college_seed, 16) % 100  # 生成0-99的索引

    # 生成明显不同的颜色变体
    variant = generate_color_variant_for_college(base_scheme, college_index)

    return variant

# 兼容性函数
def get_all_theme_names():
    """获取所有色系名称"""
    return list(BASE_COLOR_SCHEMES.keys())

def get_themes_by_category(category):
    """根据颜色系列获取主题列表"""
    return [category] if category in BASE_COLOR_SCHEMES else []

def get_theme_by_name(theme_name):
    """根据主题名称获取主题配置"""
    if theme_name in BASE_COLOR_SCHEMES:
        return generate_random_color_variant(BASE_COLOR_SCHEMES[theme_name])
    return generate_random_color_variant(BASE_COLOR_SCHEMES[DEFAULT_THEME])

# 动态生成THEME_COLORS用于兼容性
THEME_COLORS = {}
for scheme_name, scheme_data in BASE_COLOR_SCHEMES.items():
    THEME_COLORS[scheme_name] = generate_random_color_variant(scheme_data)
