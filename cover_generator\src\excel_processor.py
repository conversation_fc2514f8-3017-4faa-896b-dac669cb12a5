"""
Excel文件处理器
用于读取和处理Excel文件中的学校学院信息
"""

import os
import pandas as pd
from typing import List, Optional

class ExcelProcessor:
    def __init__(self):
        self.supported_formats = ['.xlsx', '.xls', '.csv']
    
    def read_school_list(self, file_path: str, column_index: int = 0) -> List[str]:
        """
        从Excel文件读取学校学院列表
        
        Args:
            file_path: Excel文件路径
            column_index: 列索引，默认为0（第一列）
        
        Returns:
            List[str]: 学校学院名称列表
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        file_ext = os.path.splitext(file_path)[1].lower()
        if file_ext not in self.supported_formats:
            raise ValueError(f"不支持的文件格式: {file_ext}")
        
        try:
            # 根据文件格式读取
            if file_ext == '.csv':
                df = pd.read_csv(file_path, header=None, encoding='utf-8')
            else:
                df = pd.read_excel(file_path, header=None)
            
            # 检查是否有数据
            if df.empty:
                raise ValueError("文件为空")
            
            # 检查列索引是否有效
            if column_index >= len(df.columns):
                raise ValueError(f"列索引超出范围: {column_index}")
            
            # 获取指定列的数据
            school_column = df.iloc[:, column_index]
            
            # 过滤空值并转换为字符串
            school_list = []
            for value in school_column:
                if pd.notna(value) and str(value).strip():
                    school_list.append(str(value).strip())
            
            if not school_list:
                raise ValueError("指定列中没有有效数据")
            
            print(f"✓ 成功读取 {len(school_list)} 条学校学院信息")
            return school_list
            
        except Exception as e:
            raise RuntimeError(f"读取Excel文件失败: {str(e)}")
    
    def validate_file(self, file_path: str) -> bool:
        """
        验证文件是否可以读取
        """
        try:
            self.read_school_list(file_path)
            return True
        except:
            return False
    
    def get_file_info(self, file_path: str) -> dict:
        """
        获取文件基本信息
        """
        if not os.path.exists(file_path):
            return {"error": "文件不存在"}
        
        try:
            file_ext = os.path.splitext(file_path)[1].lower()
            
            # 读取文件
            if file_ext == '.csv':
                df = pd.read_csv(file_path, header=None, encoding='utf-8')
            else:
                df = pd.read_excel(file_path, header=None)
            
            # 获取第一列的预览数据
            preview_data = []
            if not df.empty and len(df.columns) > 0:
                first_column = df.iloc[:, 0]
                for i, value in enumerate(first_column.head(5)):  # 预览前5行
                    if pd.notna(value):
                        preview_data.append(str(value).strip())
            
            return {
                "rows": len(df),
                "columns": len(df.columns),
                "preview": preview_data,
                "format": file_ext
            }
            
        except Exception as e:
            return {"error": f"读取文件失败: {str(e)}"}

# 创建全局实例
excel_processor = ExcelProcessor()
