"""
图形用户界面
提供简洁易用的GUI界面
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import os
from typing import Optional

from .controller import cover_generator
from .config import THEME_COLORS

class CoverGeneratorGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("千帆封面生成器 v1.0")
        self.root.geometry("800x700")
        self.root.resizable(True, True)
        
        # 设置主题样式
        style = ttk.Style()
        style.theme_use('vista')  # 使用现代主题
        
        # 变量
        self.excel_file_path = tk.StringVar()
        self.single_school_text = tk.StringVar()
        self.is_processing = False
        
        # 创建界面
        self._create_widgets()
        
        # 绑定关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self._on_closing)
    
    def _create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="千帆封面生成器", 
                               font=('Microsoft YaHei', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # API状态检查
        self._create_api_status_section(main_frame, row=1)
        
        # 单个生成区域
        self._create_single_generation_section(main_frame, row=2)
        
        # 批量生成区域
        self._create_batch_generation_section(main_frame, row=3)
        
        # 进度显示区域
        self._create_progress_section(main_frame, row=4)
        
        # 日志显示区域
        self._create_log_section(main_frame, row=5)
        
        # 底部按钮区域
        self._create_bottom_buttons(main_frame, row=6)
    
    def _create_api_status_section(self, parent, row):
        """创建API状态区域"""
        frame = ttk.LabelFrame(parent, text="API状态", padding="10")
        frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        frame.columnconfigure(1, weight=1)
        
        # API状态指示器
        self.api_status_label = ttk.Label(frame, text="未检测", foreground="orange")
        self.api_status_label.grid(row=0, column=0, sticky=tk.W)
        
        # 测试按钮
        test_btn = ttk.Button(frame, text="测试API连接", command=self._test_api)
        test_btn.grid(row=0, column=2, sticky=tk.E)
        
        # 自动测试
        self._test_api()
    
    def _create_single_generation_section(self, parent, row):
        """创建单个生成区域"""
        frame = ttk.LabelFrame(parent, text="单个生成", padding="10")
        frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        frame.columnconfigure(1, weight=1)
        
        # 输入标签
        ttk.Label(frame, text="学校学院名称:").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        
        # 输入框
        entry = ttk.Entry(frame, textvariable=self.single_school_text, width=50)
        entry.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 生成按钮
        generate_btn = ttk.Button(frame, text="生成封面", command=self._generate_single)
        generate_btn.grid(row=1, column=2, sticky=tk.E, padx=(10, 0))
        
        # 示例文本
        example_label = ttk.Label(frame, text="示例: 电子科技大学经济与管理学院", 
                                 foreground="gray", font=('Microsoft YaHei', 9))
        example_label.grid(row=2, column=0, columnspan=3, sticky=tk.W)
    
    def _create_batch_generation_section(self, parent, row):
        """创建批量生成区域"""
        frame = ttk.LabelFrame(parent, text="批量生成", padding="10")
        frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        frame.columnconfigure(1, weight=1)
        
        # 文件选择
        ttk.Label(frame, text="Excel文件:").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        
        file_frame = ttk.Frame(frame)
        file_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        file_frame.columnconfigure(0, weight=1)
        
        # 文件路径显示
        file_entry = ttk.Entry(file_frame, textvariable=self.excel_file_path, state="readonly")
        file_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 10))
        
        # 浏览按钮
        browse_btn = ttk.Button(file_frame, text="浏览", command=self._browse_excel_file)
        browse_btn.grid(row=0, column=1)
        
        # 批量生成按钮
        batch_btn = ttk.Button(file_frame, text="批量生成", command=self._generate_batch)
        batch_btn.grid(row=0, column=2, padx=(10, 0))
        
        # 文件信息显示
        self.file_info_label = ttk.Label(frame, text="", foreground="gray", font=('Microsoft YaHei', 9))
        self.file_info_label.grid(row=2, column=0, columnspan=3, sticky=tk.W)
    
    def _create_progress_section(self, parent, row):
        """创建进度显示区域"""
        frame = ttk.LabelFrame(parent, text="处理进度", padding="10")
        frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        frame.columnconfigure(0, weight=1)
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(frame, variable=self.progress_var, 
                                          maximum=100, length=400)
        self.progress_bar.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))
        
        # 进度文本
        self.progress_label = ttk.Label(frame, text="就绪")
        self.progress_label.grid(row=1, column=0, sticky=tk.W)
    
    def _create_log_section(self, parent, row):
        """创建日志显示区域"""
        frame = ttk.LabelFrame(parent, text="处理日志", padding="10")
        frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        frame.columnconfigure(0, weight=1)
        frame.rowconfigure(0, weight=1)
        parent.rowconfigure(row, weight=1)
        
        # 日志文本框
        self.log_text = scrolledtext.ScrolledText(frame, height=8, state=tk.DISABLED)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 清空日志按钮
        clear_btn = ttk.Button(frame, text="清空日志", command=self._clear_log)
        clear_btn.grid(row=1, column=0, sticky=tk.E, pady=(5, 0))
    
    def _create_bottom_buttons(self, parent, row):
        """创建底部按钮区域"""
        frame = ttk.Frame(parent)
        frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))
        
        # 打开输出文件夹按钮
        open_folder_btn = ttk.Button(frame, text="打开输出文件夹", command=self._open_output_folder)
        open_folder_btn.pack(side=tk.LEFT)
        
        # 关于按钮
        about_btn = ttk.Button(frame, text="关于", command=self._show_about)
        about_btn.pack(side=tk.RIGHT)
    
    def _test_api(self):
        """测试API连接"""
        def test_thread():
            try:
                self.api_status_label.config(text="检测中...", foreground="orange")
                self.root.update()
                
                success = cover_generator.test_api_connection()
                
                if success:
                    self.api_status_label.config(text="连接正常", foreground="green")
                    self._log_message("✓ API连接测试成功")
                else:
                    self.api_status_label.config(text="连接失败", foreground="red")
                    self._log_message("✗ API连接测试失败")
                    
            except Exception as e:
                self.api_status_label.config(text="连接错误", foreground="red")
                self._log_message(f"✗ API连接测试出错: {e}")
        
        thread = threading.Thread(target=test_thread, daemon=True)
        thread.start()
    
    def _browse_excel_file(self):
        """浏览Excel文件"""
        file_path = filedialog.askopenfilename(
            title="选择Excel文件",
            filetypes=[
                ("Excel文件", "*.xlsx *.xls"),
                ("CSV文件", "*.csv"),
                ("所有文件", "*.*")
            ]
        )
        
        if file_path:
            self.excel_file_path.set(file_path)
            
            # 显示文件信息
            try:
                from .excel_processor import excel_processor
                info = excel_processor.get_file_info(file_path)
                
                if "error" in info:
                    self.file_info_label.config(text=f"文件错误: {info['error']}", foreground="red")
                else:
                    preview_text = ", ".join(info["preview"][:3])
                    if len(info["preview"]) > 3:
                        preview_text += "..."
                    
                    info_text = f"共{info['rows']}行，{info['columns']}列 | 预览: {preview_text}"
                    self.file_info_label.config(text=info_text, foreground="blue")
                    
            except Exception as e:
                self.file_info_label.config(text=f"读取文件信息失败: {e}", foreground="red")
    
    def _generate_single(self):
        """生成单个封面"""
        school_text = self.single_school_text.get().strip()
        
        if not school_text:
            messagebox.showwarning("输入错误", "请输入学校学院名称")
            return
        
        if self.is_processing:
            messagebox.showwarning("处理中", "正在处理其他任务，请稍等")
            return
        
        self.is_processing = True
        self._log_message(f"开始生成单个封面: {school_text}")
        
        def progress_callback(progress, message):
            self.progress_var.set(progress)
            self.progress_label.config(text=message)
            self.root.update()
        
        def success_callback(html_path, png_path):
            self.is_processing = False
            self._log_message(f"✓ 封面生成成功:")
            self._log_message(f"  HTML: {html_path}")
            self._log_message(f"  PNG: {png_path}")
            messagebox.showinfo("成功", f"封面生成完成!\n\nHTML: {os.path.basename(html_path)}\nPNG: {os.path.basename(png_path)}")
        
        def error_callback(error_msg):
            self.is_processing = False
            self._log_message(f"✗ 生成失败: {error_msg}")
            messagebox.showerror("生成失败", error_msg)
        
        # 在新线程中生成
        def generate_thread():
            cover_generator.generate_single_cover(
                school_text, progress_callback, success_callback, error_callback
            )
        
        thread = threading.Thread(target=generate_thread, daemon=True)
        thread.start()
    
    def _generate_batch(self):
        """批量生成封面"""
        excel_path = self.excel_file_path.get().strip()
        
        if not excel_path:
            messagebox.showwarning("文件错误", "请选择Excel文件")
            return
        
        if not os.path.exists(excel_path):
            messagebox.showerror("文件错误", "选择的文件不存在")
            return
        
        if self.is_processing:
            messagebox.showwarning("处理中", "正在处理其他任务，请稍等")
            return
        
        # 确认批量生成
        result = messagebox.askyesno("确认批量生成", 
                                   f"即将开始批量生成封面。\n\n文件: {os.path.basename(excel_path)}\n\n是否继续？")
        if not result:
            return
        
        self.is_processing = True
        self._log_message(f"开始批量生成: {excel_path}")
        
        def progress_callback(current, total, message):
            if total > 0:
                progress = (current / total) * 100
                self.progress_var.set(progress)
            self.progress_label.config(text=f"({current}/{total}) {message}")
            self.root.update()
        
        def item_callback(index, school_name, success, result):
            if success:
                html_path, png_path = result
                self._log_message(f"✓ [{index+1}] {school_name} - 完成")
            else:
                self._log_message(f"✗ [{index+1}] {school_name} - 失败: {result}")
        
        def complete_callback(success_count, total_count):
            self.is_processing = False
            self._log_message(f"批量生成完成: {success_count}/{total_count} 成功")
            
            summary = cover_generator.get_output_summary()
            summary_text = f"批量生成完成!\n\n成功: {success_count}\n总计: {total_count}\n\n输出文件夹包含:\n- {summary.get('folders', 0)} 个学校文件夹\n- {summary.get('html_files', 0)} 个HTML文件\n- {summary.get('png_files', 0)} 个PNG文件"
            
            messagebox.showinfo("批量生成完成", summary_text)
        
        def error_callback(error_msg):
            self.is_processing = False
            self._log_message(f"✗ 批量生成失败: {error_msg}")
            messagebox.showerror("批量生成失败", error_msg)
        
        # 开始批量生成
        cover_generator.process_excel_file(
            excel_path, progress_callback, item_callback, 
            complete_callback, error_callback
        )
    
    def _open_output_folder(self):
        """打开输出文件夹"""
        try:
            success = cover_generator.open_output_folder()
            if not success:
                messagebox.showwarning("打开失败", "输出文件夹不存在或无法打开")
        except Exception as e:
            messagebox.showerror("错误", f"打开文件夹失败: {e}")
    
    def _clear_log(self):
        """清空日志"""
        self.log_text.config(state=tk.NORMAL)
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state=tk.DISABLED)
    
    def _log_message(self, message):
        """添加日志消息"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        
        self.log_text.config(state=tk.NORMAL)
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        self.log_text.config(state=tk.DISABLED)
    
    def _show_about(self):
        """显示关于对话框"""
        about_text = """千帆封面生成器 v1.0

功能特点：
• 智能分析学校学院信息
• 自动匹配主题色和图标
• 生成高质量HTML和PNG封面
• 支持单个和批量生成
• 基于DeepSeek AI技术

技术栈：
• Python + Tkinter
• DeepSeek API
• Playwright (HTML转图片)
• Pandas (Excel处理)

作者：AI助手
版本：1.0.0
"""
        messagebox.showinfo("关于", about_text)
    
    def _on_closing(self):
        """关闭程序时的处理"""
        if self.is_processing:
            result = messagebox.askyesno("确认退出", "正在处理任务，确定要退出吗？")
            if not result:
                return
        
        self.root.destroy()
    
    def run(self):
        """运行GUI"""
        self.root.mainloop()

# 创建全局实例
def create_gui():
    return CoverGeneratorGUI()
