<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产品封面生成器 - 厦门大学 财务管理与会计研究院</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700;900&display=swap');
        
        /* --- 全局样式和主题配色 --- */
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f8fafc;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            padding: 20px;
            margin: 0;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        #cover-container {
            width: 750px;
            height: 1000px;
            /* 背景色：主题渐变 */
            background: linear-gradient(145deg, #ca8a04 0%, #f59e0b 100%);
            color: #FFFFFF;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            padding: 50px;
            box-sizing: border-box;
            border-radius: 8px;
            overflow: hidden;
            position: relative;
            text-align: center;
            box-shadow: 0 15px 30px rgba(0,0,0,0.15);
        }

        /* --- 装饰性元素 --- */
        #cover-container::before {
            content: '';
            position: absolute;
            top: -150px;
            left: -200px;
            width: 600px;
            height: 600px;
            background: radial-gradient(circle, rgba(252, 211, 77, 0.12) 0%, rgba(251, 191, 36, 0) 70%);
            transform: rotate(45deg);
        }
        #cover-container::after {
            content: '';
            position: absolute;
            bottom: -200px;
            right: -200px;
            width: 550px;
            height: 550px;
            background: radial-gradient(circle, rgba(252, 211, 77, 0.12) 0%, rgba(251, 191, 36, 0) 70%);
            border-radius: 50%;
        }

        .main-focus {
            z-index: 1;
            margin-top: 40px;
        }
        
        /* --- 学院名称样式 --- */
        .main-focus .school-name {
            font-size: 28px;
            font-weight: 500;
            background-color: rgba(0, 0, 0, 0.2);
            color: #fcd34d;
            padding: 12px 35px;
            border-radius: 50px;
            display: inline-block;
            margin-bottom: 30px;
            border: 1px solid rgba(252, 211, 77, 0.3);
        }

        .main-focus .main-title {
            font-weight: 900;
            text-shadow: none;
            color: #FFFFFF;
            margin: 0;
            line-height: 1.2;
        }
        
        /* --- 标题样式 --- */
        .main-title span {
            display: block;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        .main-title .line-1 {
            font-size: 60px;  
            font-weight: 700;
            color: #fef3c7;
        }
        .main-title .line-2 {
            font-size: 80px;
            color: #FFFFFF;
            margin-top: 10px;
            letter-spacing: 2px;
        }

        .subtitle {
            font-size: 24px;
            font-weight: 400;
            color: rgba(249, 250, 251, 0.8);
            margin-top: 25px;
            z-index: 1;
        }

        .features-grid {
            margin-top: 40px;  
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 25px;
            z-index: 1;
        }

        /* --- 特点项目样式 --- */
        .feature-item {
            background-color: rgba(0,0,0,0.2);
            border-radius: 8px;
            padding: 25px;
            box-sizing: border-box;
            border-top: 3px solid rgba(252, 211, 77, 0.6);
            text-align: left;
            transition: transform 0.2s ease-in-out, background-color 0.2s ease-in-out;
        }
        
        .feature-item:hover {
            transform: translateY(-5px);
            background-color: rgba(0,0,0,0.3);
        }

        /* --- SVG图标样式 --- */
        .feature-item .icon {
            color: #fcd34d;
            margin-bottom: 15px;
            display: block;
        }
        .feature-item .icon svg {
            width: 38px;
            height: 38px;
        }

        .feature-item h3 {
            font-size: 20px;
            font-weight: 700;
            margin: 0 0 10px 0;
            color: #fff;
        }

        .feature-item p {
            font-size: 15px;
            font-weight: 400;
            color: rgba(249, 250, 251, 0.85);
            margin: 0;
            line-height: 1.6;
        }
        
        .footer {
            text-align: center;
            font-size: 16px;
            font-weight: 500;
            color: rgba(249, 250, 251, 0.65);
            z-index: 1;
            padding-bottom: 20px;
        }

    </style>
</head>
<body>

    <div id="cover-container">
        <div class="main-focus">
            <div class="school-name">厦门大学 · 财务管理与会计研究院</div>
            <h1 class="main-title">
                <span class="line-1">保研夏令营 + 预推免</span>
                <span class="line-2">核心资料合集</span>
            </h1>
        </div>
        
        <div class="subtitle">
            精准定位 | 高效备战 | 全面领航
        </div>
        
        <div class="features-grid">
            <div class="feature-item">
                <div class="icon">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 18 9 11.25l4.306 4.306a11.95 11.95 0 0 1 5.814-5.518l2.74-1.22m0 0-5.94-2.281m5.94 2.28-2.28 5.941" />
        </svg>
                </div>
                <h3>3万字真题及解析</h3>
                <p>全面覆盖经济学原理、管理学基础、数据分析等核心课程，深入分析近年面试真题与考核要点，提供专业解题思路与应对策略。</p>
            </div>
            <div class="feature-item">
                <div class="icon">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M10.5 6a7.5 7.5 0 1 0 7.5 7.5h-7.5V6Z" />
            <path stroke-linecap="round" stroke-linejoin="round" d="M13.5 10.5H21A7.5 7.5 0 0 0 13.5 3v7.5Z" />
        </svg>
                </div>
                <h3>2025最新导师评价信息</h3>
                <p>整合全网真实学生评价与经管学院官方信息，深度分析导师研究方向、指导风格与招生偏好，助你精准匹配科技管理领域导师。</p>
            </div>
            <div class="feature-item">
                <div class="icon">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M20.25 14.15v4.25c0 1.094-.787 2.036-1.872 2.18-2.087.277-4.216.42-6.378.42s-4.291-.143-6.378-.42c-1.085-.144-1.872-1.086-1.872-2.18v-4.25m16.5 0a2.18 2.18 0 0 0 .75-1.661V8.706c0-1.081-.768-2.015-1.837-2.175a48.114 48.114 0 0 0-3.413-.387m4.5 8.006c-.194.165-.42.295-.673.38A23.978 23.978 0 0 1 12 15.75c-2.648 0-5.195-.429-7.577-1.22a2.016 2.016 0 0 1-.673-.38m0 0A2.18 2.18 0 0 1 3 12.489V8.706c0-1.081.768-2.015 1.837-2.175a48.111 48.111 0 0 1 3.413-.387m7.5 0V5.25A2.25 2.25 0 0 0 13.5 3h-3a2.25 2.25 0 0 0-2.25 2.25v.894m7.5 0a48.667 48.667 0 0 0-7.5 0M12 12.75h.008v.008H12v-.008Z" />
        </svg>
                </div>
                <h3>保研文书材料大合集</h3>
                <p>提供完整个人陈述、研究计划、简历模板与写作指南，附带高分示例与专家点评，助力文书脱颖而出。</p>
            </div>
            <div class="feature-item">
                <div class="icon">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M10.34 15.84c-.688-.06-1.386-.09-2.09-.09H7.5a4.5 4.5 0 1 1 0-9h.75c.704 0 1.402-.03 2.09-.09m0 9.18c.253.962.584 1.892.985 2.783.247.55.06 1.21-.463 1.511l-.657.38c-.551.318-1.26.117-1.527-.461a20.845 20.845 0 0 1-1.44-4.282m3.102.069a18.03 18.03 0 0 1-.59-4.59c0-1.586.205-3.124.59-4.59m0 9.18a23.848 23.848 0 0 1 8.835 2.535M10.34 6.66a23.847 23.847 0 0 0 8.835-2.535m0 0A23.74 23.74 0 0 0 18.795 3m.38 1.125a23.91 23.91 0 0 1 1.014 5.395m-1.014 8.855c-.118.38-.245.754-.38 1.125m.38-1.125a23.91 23.91 0 0 0 1.014-5.395m0-3.46c.495.413.811 1.035.811 1.73 0 .695-.316 1.317-.811 1.73m0-3.46a24.347 24.347 0 0 1 0 3.46" />
        </svg>
                </div>
                <h3>核心考点配套复习资料</h3>
                <p>精炼经济学基础、信息管理、电子商务等核心考点，整合配套复习资料与习题详解，构建科技管理系统知识体系。</p>
            </div>
        </div>

        <div class="footer">
            保研喵学姐团队 倾心整理 | 2025版
        </div>
    </div>

</body>
</html>
