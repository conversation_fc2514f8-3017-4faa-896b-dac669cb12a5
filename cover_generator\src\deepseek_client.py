"""
DeepSeek API客户端
用于智能提取学校信息和推荐主题色
"""

import openai
import json
import re
from typing import Dict, Tu<PERSON>, Optional
from .config import DEEPSEEK_CONFIG, THEME_COLORS

class DeepSeekClient:
    def __init__(self):
        self.client = openai.OpenAI(
            api_key=DEEPSEEK_CONFIG["api_key"],
            base_url=DEEPSEEK_CONFIG["base_url"]
        )
        self.model = DEEPSEEK_CONFIG["model"]
        self.timeout = DEEPSEEK_CONFIG["timeout"]
    
    def analyze_school_info(self, school_text: str) -> Dict:
        """
        分析学校学院信息，提取大学名和学院名，推荐主题色
        
        Args:
            school_text: 学校学院文本，如"电子科技大学经济与管理学院"
        
        Returns:
            Dict: {
                "university": "电子科技大学",
                "college": "经济与管理学院", 
                "theme_color": "科技蓝",
                "description": "分析描述"
            }
        """
        # 构建提示词
        theme_options = list(THEME_COLORS.keys())
        prompt = f"""
请分析以下学校学院信息，并完成两个任务：

1. 准确提取大学名称和学院名称
2. 从以下主题色选项中推荐最适合的一个主题色

学校学院信息：{school_text}

可选主题色及其特点：
- 科技蓝：适合电子信息、计算机、科技类学院
- 商务绿：适合商学院、商务类学院  
- 财经金：适合经济、金融、财政、会计类学院
- 管理紫：适合管理学院、MBA、MPA等管理类
- 经济红：适合经济学院、政法类学院
- 学院蓝：通用学院色，适合其他类型学院

请以JSON格式返回结果，格式如下：
{{
    "university": "大学名称",
    "college": "学院名称",
    "theme_color": "推荐主题色名称",
    "description": "推荐理由"
}}

注意：
1. 准确分离大学名和学院名
2. 主题色必须从上述选项中选择
3. 推荐理由要简洁明了
"""

        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "你是一个专业的教育信息分析助手，擅长分析学校学院信息并提供专业建议。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.3,
                timeout=self.timeout
            )
            
            content = response.choices[0].message.content.strip()
            
            # 尝试解析JSON
            try:
                # 提取JSON部分
                json_match = re.search(r'\{.*\}', content, re.DOTALL)
                if json_match:
                    result = json.loads(json_match.group())
                else:
                    raise ValueError("无法找到JSON格式的响应")
                
                # 验证必要字段
                required_fields = ["university", "college", "theme_color"]
                for field in required_fields:
                    if field not in result:
                        raise ValueError(f"缺少必要字段: {field}")
                
                # 验证主题色是否在可选范围内
                if result["theme_color"] not in theme_options:
                    print(f"警告：AI推荐的主题色 '{result['theme_color']}' 不在可选范围内，使用默认主题色")
                    result["theme_color"] = "科技蓝"  # 默认主题色
                
                return result
                
            except (json.JSONDecodeError, ValueError) as e:
                print(f"AI响应解析失败: {e}")
                print(f"原始响应: {content}")
                # 使用备用解析方法
                return self._fallback_parse(school_text)
                
        except Exception as e:
            print(f"调用DeepSeek API失败: {e}")
            # 使用备用解析方法
            return self._fallback_parse(school_text)
    
    def _fallback_parse(self, school_text: str) -> Dict:
        """
        备用解析方法，当AI调用失败时使用简单规则解析
        """
        print("使用备用解析方法...")
        
        # 简单的规则解析
        university = ""
        college = ""
        theme_color = "科技蓝"  # 默认主题色
        
        # 常见大学名称模式
        university_patterns = [
            r"(.+?大学)",
            r"(.+?学院)(?=·|\.|\s|$)",
            r"(.+?科技)",
            r"(.+?理工)",
            r"(.+?财经)",
            r"(.+?师范)"
        ]
        
        for pattern in university_patterns:
            match = re.search(pattern, school_text)
            if match:
                university = match.group(1) + ("大学" if not match.group(1).endswith("大学") else "")
                break
        
        # 提取学院名称
        if university:
            # 移除大学名称后的部分作为学院名
            college_part = school_text.replace(university, "").strip("·").strip()
            if college_part:
                college = college_part
        
        # 如果没有提取到大学名，尝试其他方法
        if not university:
            if "大学" in school_text:
                parts = school_text.split("大学")
                if len(parts) >= 2:
                    university = parts[0] + "大学"
                    college = parts[1].strip("·").strip()
            else:
                # 如果没有"大学"，可能是学院名
                college = school_text
                university = "未知大学"
        
        # 根据关键词推荐主题色
        if any(keyword in school_text for keyword in ["科技", "电子", "信息", "计算机"]):
            theme_color = "科技蓝"
        elif any(keyword in school_text for keyword in ["商学", "商务", "工商"]):
            theme_color = "商务绿"
        elif any(keyword in school_text for keyword in ["经济", "金融", "财政", "会计", "财务"]):
            theme_color = "财经金"
        elif any(keyword in school_text for keyword in ["管理", "MBA", "MPA"]):
            theme_color = "管理紫"
        elif any(keyword in school_text for keyword in ["政法", "法学", "政治"]):
            theme_color = "经济红"
        elif any(keyword in school_text for keyword in ["国际", "外语", "外国语"]):
            theme_color = "学院蓝"
        
        return {
            "university": university or "未知大学",
            "college": college or "未知学院",
            "theme_color": theme_color,
            "description": "备用解析结果"
        }
    
    def test_connection(self) -> bool:
        """
        测试API连接是否正常
        """
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "user", "content": "你好，请回复'连接成功'"}
                ],
                max_tokens=10,
                timeout=10
            )
            return "连接成功" in response.choices[0].message.content
        except Exception as e:
            print(f"API连接测试失败: {e}")
            return False

# 创建全局实例
deepseek_client = DeepSeekClient()
