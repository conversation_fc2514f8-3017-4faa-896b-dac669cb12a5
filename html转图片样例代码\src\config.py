"""
HTML转图片工具 - 配置文件
包含程序的各种配置选项
"""

# 默认配置
DEFAULT_CONFIG = {
    "viewport": {
        "width": 1920,
        "height": 1080
    },
    "quality_settings": {
        "标准清晰度": {"scale": 1, "dpi": 96},
        "高清(HD)": {"scale": 1.5, "dpi": 144},
        "全高清(FHD)": {"scale": 2, "dpi": 192},
        "超高清(4K)": {"scale": 3, "dpi": 288},
        "8K超清": {"scale": 4, "dpi": 384},
        "矢量级别": {"scale": 5, "dpi": 480}
    },
    "format_map": {
        "PNG": "png",
        "SVG": "svg", 
        "PDF": "pdf",
        "WEBP": "webp",
        "JPEG": "jpg"
    },    "browser_args": [
        '--high-dpi-support=1',
        '--force-color-profile=srgb',
        '--disable-gpu-sandbox',
        '--disable-software-rasterizer',
        '--disable-web-security',
        '--allow-file-access-from-files',
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-background-timer-throttling',
        '--disable-backgrounding-occluded-windows',
        '--disable-renderer-backgrounding',
        # 内存和性能优化
        '--memory-pressure-off',
        '--max_old_space_size=8192',  # 增加V8内存限制到8GB
        '--disable-background-networking',
        '--disable-default-apps',
        '--disable-extensions',
        '--disable-sync',
        '--disable-translate',
        '--hide-scrollbars',
        # 截图优化
        '--enable-accelerated-2d-canvas',
        '--enable-gpu-rasterization',
        '--force-gpu-rasterization',
        '--disable-gpu-driver-bug-workarounds',
        '--enable-experimental-web-platform-features',
        # 大页面支持
        '--max-texture-size=32768',  # 增加纹理大小限制
        '--enable-unsafe-webgpu'  # 启用更强大的WebGPU
    ],
    "auto_crop": True,  # 自动裁剪白边
    "crop_threshold": 240,  # 白色阈值，超过此值认为是白色
    "crop_padding": 20  # 裁剪后保留的边距
}

# UI配置
UI_CONFIG = {
    "window_size": "900x700",
    "bg_color": "#f0f0f0",
    "title": "HTML转图片工具 - 智能裁剪版",
    "fonts": {
        "title": ("微软雅黑", 16, "bold"),
        "label": ("微软雅黑", 10),
        "entry": ("微软雅黑", 9),
        "button": ("微软雅黑", 9),
        "info": ("微软雅黑", 8)
    },
    "colors": {
        "primary": "#4CAF50",
        "secondary": "#2196F3", 
        "accent": "#FF9800",
        "text": "#333333",
        "info": "#666666",
        "status_bg": "#e0e0e0"    }
}

# 单独导出的设置
QUALITY_SETTINGS = DEFAULT_CONFIG["quality_settings"]
FORMAT_SETTINGS = DEFAULT_CONFIG["format_map"]
