"""
HTML转图片工具 - 功能模块包
智能裁剪版 v2.0

包含以下模块：
- 配置文件: 应用配置和常量定义
- 界面模块: 用户界面组件
- 截图模块: 网页截图功能
- 图像处理模块: 图像裁剪和处理
- 控制器模块: 业务逻辑控制

作者：AI助手
版本：2.0
"""

# 导入配置
from .config import DEFAULT_CONFIG, QUALITY_SETTINGS, FORMAT_SETTINGS

# 导入主要类
try:
    # 直接使用tkinter界面
    from .gui import HTMLToImageGUI, MinimalGUI
    GUI_TYPE = "Tkinter"
except ImportError:
    # 如果tkinter界面不可用，使用最小的界面
    from .gui import MinimalGUI
    HTMLToImageGUI = MinimalGUI
    GUI_TYPE = "Minimal"

from .screenshot import ScreenshotCapture
from .image_processor import ImageProcessor
from .controller import ScreenshotHandler
from .pdf_processor import PDFProcessor

# 导出的公共接口
__all__ = [
    'DEFAULT_CONFIG',
    'QUALITY_SETTINGS', 
    'FORMAT_SETTINGS',
    'HTMLToImageGUI',
    'MinimalGUI',
    'ScreenshotCapture',
    'ScreenshotHandler',
    'ImageProcessor',
    'PDFProcessor',
    'GUI_TYPE'
]

# 版本信息
__version__ = '2.0'
__author__ = 'AI助手'
__description__ = 'HTML转图片工具 - 智能裁剪版'
