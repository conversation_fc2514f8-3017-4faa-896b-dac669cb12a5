"""
HTML转图片工具 - 图像处理模块
包含图像裁剪、优化等功能
"""

import os
from PIL import Image, ImageOps
import numpy as np
import cv2


class ImageProcessor:
    """图像处理类"""
    
    def __init__(self, crop_threshold=240, crop_padding=20):
        self.crop_threshold = crop_threshold
        self.crop_padding = crop_padding
    
    def auto_crop_white_borders(self, image_path):
        """
        自动裁剪图片的白边
        
        Args:
            image_path: 图片文件路径
            
        Returns:
            bool: 是否成功裁剪
        """
        try:
            # 使用PIL读取图片
            with Image.open(image_path) as img:
                # 转换为RGB模式（如果不是的话）
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # 转换为numpy数组
                img_array = np.array(img)
                
                # 计算灰度值
                gray = np.mean(img_array, axis=2)
                
                # 找到非白色区域的边界
                mask = gray < self.crop_threshold
                
                # 获取有内容的行和列
                rows = np.any(mask, axis=1)
                cols = np.any(mask, axis=0)
                
                # 找到边界
                if not np.any(rows) or not np.any(cols):
                    # 如果整个图片都是白色，不进行裁剪
                    return False
                
                top, bottom = np.where(rows)[0][[0, -1]]
                left, right = np.where(cols)[0][[0, -1]]
                
                # 添加边距
                height, width = img_array.shape[:2]
                top = max(0, top - self.crop_padding)
                bottom = min(height, bottom + self.crop_padding)
                left = max(0, left - self.crop_padding)
                right = min(width, right + self.crop_padding)
                
                # 裁剪图片
                cropped_img = img.crop((left, top, right, bottom))
                
                # 保存裁剪后的图片
                cropped_img.save(image_path, quality=95 if image_path.lower().endswith('.jpg') else None)
                
                return True
                
        except Exception as e:
            print(f"裁剪图片时出错: {e}")
            return False
    
    def optimize_image_quality(self, image_path, format_type):
        """
        优化图片质量
        
        Args:
            image_path: 图片文件路径
            format_type: 图片格式类型
        """
        try:
            with Image.open(image_path) as img:
                # 根据格式进行优化
                if format_type.upper() == 'JPEG':
                    # JPEG优化
                    img.save(image_path, format='JPEG', quality=98, optimize=True)
                elif format_type.upper() == 'PNG':
                    # PNG优化
                    img.save(image_path, format='PNG', optimize=True)
                elif format_type.upper() == 'WEBP':
                    # WebP优化
                    img.save(image_path, format='WEBP', quality=100, lossless=True)
                    
        except Exception as e:
            print(f"优化图片时出错: {e}")
    
    def get_cropped_dimensions(self, image_path):
        """
        获取裁剪后的图片尺寸信息
        
        Args:
            image_path: 图片文件路径
            
        Returns:
            dict: 包含尺寸信息的字典
        """
        try:
            with Image.open(image_path) as img:
                width, height = img.size
                file_size = os.path.getsize(image_path) / (1024 * 1024)  # MB
                
                return {
                    'width': width,
                    'height': height,
                    'file_size': file_size,
                    'aspect_ratio': width / height
                }
        except Exception as e:
            print(f"获取图片信息时出错: {e}")
            return None
    
    def create_thumbnail(self, image_path, thumbnail_path, size=(200, 200)):
        """
        创建缩略图
        
        Args:
            image_path: 原图片路径
            thumbnail_path: 缩略图保存路径
            size: 缩略图尺寸
        """
        try:
            with Image.open(image_path) as img:
                img.thumbnail(size, Image.Resampling.LANCZOS)
                img.save(thumbnail_path)
        except Exception as e:
            print(f"创建缩略图时出错: {e}")


class WhiteBorderDetector:
    """白边检测器"""
    
    def __init__(self, threshold=240):
        self.threshold = threshold
    
    def detect_borders(self, image_path):
        """
        检测图片的白边区域
        
        Args:
            image_path: 图片文件路径
            
        Returns:
            dict: 包含边界信息的字典
        """
        try:
            with Image.open(image_path) as img:
                img_array = np.array(img.convert('RGB'))
                gray = np.mean(img_array, axis=2)
                
                # 检测各边的白边厚度
                height, width = gray.shape
                
                # 检测顶部白边
                top_border = 0
                for i in range(height):
                    if np.mean(gray[i, :]) < self.threshold:
                        break
                    top_border += 1
                
                # 检测底部白边
                bottom_border = 0
                for i in range(height - 1, -1, -1):
                    if np.mean(gray[i, :]) < self.threshold:
                        break
                    bottom_border += 1
                
                # 检测左边白边
                left_border = 0
                for i in range(width):
                    if np.mean(gray[:, i]) < self.threshold:
                        break
                    left_border += 1
                
                # 检测右边白边
                right_border = 0
                for i in range(width - 1, -1, -1):
                    if np.mean(gray[:, i]) < self.threshold:
                        break
                    right_border += 1
                
                return {
                    'top': top_border,
                    'bottom': bottom_border,
                    'left': left_border,
                    'right': right_border,
                    'total_width': width,
                    'total_height': height
                }
                
        except Exception as e:
            print(f"检测白边时出错: {e}")
            return None
