# 千帆封面图生成工具 - 项目结构说明

## 📁 完整目录结构

```
生成封面代码/
├── 📋 启动文件
│   ├── 启动程序.bat          # Windows智能启动脚本（推荐）
│   ├── run.bat              # Windows简单启动脚本
│   ├── start.py             # 跨平台Python启动脚本
│   ├── start.sh             # Linux/Mac启动脚本
│   └── install.bat          # Windows依赖安装脚本
│
├── 📖 说明文档
│   ├── README.md            # 详细项目说明
│   ├── 使用指南.md          # 用户使用指南
│   ├── 快速启动.txt         # 快速启动说明
│   └── 项目结构说明.md      # 本文件
│
├── 🚀 主程序
│   ├── main.py              # 主程序入口
│   └── requirements.txt     # Python依赖包列表
│
├── ⚙️ 配置模块 (config/)
│   ├── __init__.py          # 模块初始化
│   ├── settings.py          # 基本配置和API设置
│   ├── themes.py            # 23种主题色配置
│   └── icons.py             # 26种图标配置
│
├── 🔧 核心模块 (src/)
│   ├── __init__.py          # 模块初始化
│   ├── api_client.py        # DeepSeek API客户端
│   ├── color_database.py    # 颜色记录数据库
│   ├── excel_reader.py      # Excel文件读取
│   ├── html_processor.py    # HTML模板处理
│   ├── image_converter.py   # HTML转图片
│   ├── file_manager.py      # 文件管理
│   └── gui.py               # GUI图形界面
│
├── 📄 模板文件 (templates/)
│   └── cover_template.html  # 封面HTML模板
│
├── 📁 输出目录 (output/)
│   └── [生成的封面文件]     # 按学校学院分类存放
│
├── 🎨 资源文件 (assets/)
│   └── icons/               # 额外图标资源
│
├── 🧪 测试文件
│   ├── test_data.py         # 测试数据生成
│   ├── test_enhanced_features.py  # 功能测试
│   └── demo_enhanced_features.py  # 功能演示
│
└── 💾 数据文件
    └── school_colors.json   # 学校颜色使用记录
```

## 🚀 启动方式优先级

### Windows用户
1. **启动程序.bat** ⭐⭐⭐⭐⭐
   - 最智能的启动方式
   - 自动检查Python环境
   - 自动检查文件完整性
   - 友好的错误提示

2. **run.bat** ⭐⭐⭐⭐
   - 简单快速启动
   - 基本环境检查
   - 适合日常使用

3. **start.py** ⭐⭐⭐
   - 跨平台通用
   - 自动安装依赖
   - 命令行运行

### Linux/Mac用户
1. **start.sh** ⭐⭐⭐⭐⭐
   - 专为Unix系统优化
   - 彩色输出界面
   - 完整环境检查

2. **start.py** ⭐⭐⭐⭐
   - 跨平台通用
   - Python环境启动

## 📋 核心功能模块

### 🎨 主题色系统 (themes.py)
- **23种专业主题色**
- **8个色系分类**：蓝、绿、红、紫、橙、金、灰、青
- **智能颜色避重**：同校不同学院自动使用不同颜色

### 📊 图标系统 (icons.py)
- **26种精选图标**
- **12个功能分类**：数据分析、科技、商务、财务等
- **智能图标匹配**：根据学院类型自动选择合适图标

### 🧠 智能识别 (api_client.py)
- **DeepSeek AI集成**：自动识别大学和学院名称
- **智能主题推荐**：根据学院特点推荐最适合的主题色
- **备用分析方案**：API不可用时的本地分析

### 💾 颜色数据库 (color_database.py)
- **使用记录追踪**：记录每个学校已使用的颜色
- **智能避重算法**：自动避免同校颜色重复
- **持久化存储**：使用JSON文件保存历史记录

### 🖼️ 图片生成 (image_converter.py)
- **高清转换**：HTML转PNG，支持2-4倍缩放
- **自动优化**：字体渲染优化、自动裁剪白边
- **批量处理**：支持大批量图片生成

### 📋 文件管理 (file_manager.py)
- **标准化命名**：统一的文件和文件夹命名规则
- **自动组织**：按学校学院自动创建文件夹结构
- **批量管理**：支持批量文件操作

## 🔧 配置说明

### API配置 (settings.py)
```python
'deepseek_api': {
    'api_key': '***********************************',  # 预设API密钥
    'base_url': 'https://api.deepseek.com',
    'model': 'deepseek-chat'
}
```

### 图片质量配置
```python
'screenshot': {
    'scale': 2.0,        # 高清缩放比例
    'auto_crop': True,   # 自动裁剪白边
    'format': 'PNG'      # 输出格式
}
```

## 📊 数据文件

### school_colors.json
记录每个学校已使用的颜色，格式如下：
```json
{
  "电子科技大学": {
    "经济与管理学院": {
      "theme_color": "科技蓝",
      "timestamp": "2025-01-12 15:30:45"
    },
    "计算机科学与工程学院": {
      "theme_color": "深海蓝",
      "timestamp": "2025-01-12 15:31:20"
    }
  }
}
```

## 🎯 使用建议

1. **首次使用**：运行 `install.bat` 安装依赖
2. **日常使用**：双击 `启动程序.bat` 启动
3. **批量处理**：准备Excel文件，使用批量处理功能
4. **问题排查**：查看启动脚本的详细错误信息

## 🔄 更新和维护

- **配置更新**：修改 `config/` 下的配置文件
- **模板更新**：修改 `templates/cover_template.html`
- **功能扩展**：在 `src/` 下添加新模块
- **测试验证**：运行 `test_enhanced_features.py`

---

**项目特点**：模块化设计、智能化处理、用户友好、跨平台兼容
