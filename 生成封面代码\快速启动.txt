🚀 千帆封面图生成工具 - 快速启动指南

═══════════════════════════════════════════════════════════════

📋 启动方式（按推荐程度排序）

Windows用户：
1. 【推荐】双击 "start.bat"           ← 最简单，直接启动
2. 双击 "run.bat"                    ← 带检查的启动
3. 双击 "启动.bat"                   ← 中文界面版本
4. 双击 "启动程序.bat"               ← 最详细的检查
5. 命令行运行 "python start.py"      ← 跨平台通用
6. 双击 "install.bat" 然后启动       ← 首次使用

Linux/Mac用户：
1. 终端运行 "./start.sh"          ← 推荐方式
2. 终端运行 "python3 start.py"    ← 通用方式
3. 终端运行 "python3 main.py"     ← 直接启动

═══════════════════════════════════════════════════════════════

🔧 如果启动失败

1. 检查Python版本
   - 需要Python 3.8或更高版本
   - Windows: 在命令行输入 "python --version"
   - Linux/Mac: 在终端输入 "python3 --version"

2. 安装依赖包
   - 双击运行 "install.bat" (Windows)
   - 或命令行运行 "pip install -r requirements.txt"

3. 安装playwright浏览器
   - 命令行运行 "playwright install chromium"

4. 检查文件完整性
   - 确保存在 main.py、config/、src/ 等文件

═══════════════════════════════════════════════════════════════

💡 常见问题

Q: 提示"未找到Python"？
A: 请安装Python并添加到PATH环境变量
   下载地址: https://www.python.org/

Q: 依赖包安装失败？
A: 尝试使用国内镜像源:
   pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/

Q: playwright安装失败？
A: 检查网络连接，或稍后手动安装:
   playwright install chromium

Q: 程序启动后没有界面？
A: 检查是否安装了tkinter（Python内置GUI库）

═══════════════════════════════════════════════════════════════

📞 获取帮助

- 详细说明: README.md
- 使用教程: 使用指南.md
- 功能演示: python demo_enhanced_features.py
- 功能测试: python test_enhanced_features.py

═══════════════════════════════════════════════════════════════

🎯 首次使用建议

1. 双击 "install.bat" 安装所有依赖
2. 双击 "启动程序.bat" 启动软件
3. 在"单个处理"选项卡测试功能
4. 查看"使用指南.md"了解详细功能

祝您使用愉快！ 🎉
