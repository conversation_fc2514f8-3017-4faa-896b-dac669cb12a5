# -*- coding: utf-8 -*-
"""
千帆封面图自动生成工具
主程序入口

功能特点：
1. 支持Excel批量处理和单个学院处理
2. 智能识别学院名称并推荐主题色
3. 自动生成HTML封面并转换为高清PNG图片
4. 标准化文件管理和输出

作者：AI助手
版本：1.0.0
"""

import sys
import os
import traceback
from tkinter import messagebox

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

try:
    from src.gui import CoverGeneratorGUI
    from config.settings import APP_CONFIG
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保所有依赖包已正确安装")
    sys.exit(1)


class CoverGeneratorApp:
    """封面生成器应用主类"""
    
    def __init__(self):
        self.config = APP_CONFIG
        self.gui = None
        
    def initialize(self):
        """初始化应用"""
        try:
            # 创建输出目录
            os.makedirs(self.config['output_dir'], exist_ok=True)
            
            # 初始化GUI
            self.gui = CoverGeneratorGUI()
            
            print(f"✨ 千帆封面图生成工具已启动")
            print(f"输出目录: {self.config['output_dir']}")
            return True
            
        except Exception as e:
            error_msg = f"应用初始化失败: {str(e)}\n\n详细错误:\n{traceback.format_exc()}"
            print(error_msg)
            try:
                messagebox.showerror("初始化错误", error_msg)
            except:
                pass
            return False
    
    def run(self):
        """运行应用"""
        if not self.initialize():
            return False
            
        try:
            print("启动千帆封面图生成工具...")
            print("程序已启动，等待用户操作...")
            
            # 运行GUI界面
            self.gui.run()
            print("程序已退出")
            return True
            
        except KeyboardInterrupt:
            print("\n用户中断程序")
            return True
            
        except Exception as e:
            error_msg = f"程序运行时发生错误: {str(e)}\n\n详细错误:\n{traceback.format_exc()}"
            print(error_msg)
            try:
                messagebox.showerror("运行时错误", error_msg)
            except:
                pass
            return False


def check_dependencies():
    """检查依赖包"""
    required_packages = [
        ('playwright', 'playwright'),
        ('PIL', 'Pillow'),
        ('pandas', 'pandas'),
        ('openpyxl', 'openpyxl'),
        ('openai', 'openai'),
        ('tkinter', '内置模块')
    ]
    
    missing_packages = []
    
    for package_name, pip_name in required_packages:
        try:
            if package_name == 'tkinter':
                import tkinter
            else:
                __import__(package_name)
            print(f"✓ {package_name} 已安装")
        except ImportError:
            if pip_name != '内置模块':
                missing_packages.append(pip_name)
            print(f"✗ {package_name} 未安装")
    
    if missing_packages:
        error_msg = f"""
缺少以下必需依赖包，请先安装：

{chr(10).join(f'pip install {pkg}' for pkg in missing_packages)}

或者运行以下命令一次性安装所有依赖：
pip install {' '.join(missing_packages)}

注意：playwright安装后还需要运行：
playwright install chromium
"""
        print(error_msg)
        try:
            messagebox.showerror("依赖包缺失", error_msg)
        except:
            pass
        return False
    
    print("✓ 核心依赖包检查通过")
    return True


def main():
    """主函数"""
    print("=" * 60)
    print("✨ 千帆封面图自动生成工具 v1.0.0")
    print("=" * 60)
    
    # 检查依赖
    if not check_dependencies():
        input("按回车键退出...")
        return 1
    
    try:
        # 创建并运行应用
        app = CoverGeneratorApp()
        success = app.run()
        
        return 0 if success else 1
        
    except Exception as e:
        error_msg = f"程序启动失败: {str(e)}\n\n详细错误:\n{traceback.format_exc()}"
        print(error_msg)
        try:
            messagebox.showerror("启动错误", error_msg)
        except:
            pass
        input("按回车键退出...")
        return 1


if __name__ == "__main__":
    sys.exit(main())
